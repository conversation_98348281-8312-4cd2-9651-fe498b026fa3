# PowerShell script to fix image paths in index1.html
$content = Get-Content 'index1.html' -Raw

# Replace all img src paths
$content = $content -replace 'src="img/', 'src="@/assets/img/'

# Replace all href paths
$content = $content -replace 'href="img/', 'href="@/assets/img/'

# Replace all background-image url paths
$content = $content -replace 'url\(img/', 'url(@/assets/img/'

# Save the updated content
Set-Content 'index1.html' -Value $content -NoNewline

Write-Host "Image paths updated successfully!"
