<script setup>
import {useHeaderSticky} from "@/utils/common.js";
import {computed, onMounted} from "vue";
import IndexHeader from "@/components/IndexHeader.vue";
import IndexFooter from "@/components/IndexFooter.vue";
import BackToTop from "@/components/BackToTop.vue";
import TopHeader from "@/components/TopHeader.vue";
import {useRoute} from "vue-router";

onMounted(useHeaderSticky);

const route = useRoute();
const isAuthPage = computed( () => route.name === 'login' || route.name === 'register' );
</script>

<template>
  <div>
    <top-header v-if="!isAuthPage"/>
    <index-header v-if="!isAuthPage"/>
    <!-- Smooth Body -->
    <div class="smooth-body" >
      <div id="smooth-content">
        <router-view/>
        <index-footer v-if="!isAuthPage"/>
      </div>
    </div>
    <back-to-top/>
  </div>

</template>

<style scoped></style>
