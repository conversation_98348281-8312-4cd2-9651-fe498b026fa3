<script setup>
import {useHeaderSticky} from "@/utils/common.js";
import {computed, onMounted} from "vue";
import IndexHeader from "@/components/IndexHeader.vue";
import IndexFooter from "@/components/IndexFooter.vue";
import BackToTop from "@/components/BackToTop.vue";
import TopHeader from "@/components/TopHeader.vue";
import {useRoute} from "vue-router";
import request from "@/net/index.js";
import {getToken} from "@/utils/token.js";
import {useAccount} from "@/stores/user.js";

onMounted(useHeaderSticky);

const route = useRoute();
const isAuthPage = computed( () => route.name === 'login' || route.name === 'register' );
const account = useAccount();

onMounted(() => {
  if (getToken()){
    request.get("/getInfo").then(({ data }) => {
      Object.assign(account.info, data.user)
    });
    request.get("/system/item/count").then(({ data }) => account.cart.count = data.data);
  }
})
</script>

<template>
  <div>
    <top-header v-if="!isAuthPage"/>
    <index-header v-if="!isAuthPage"/>
    <!-- Smooth Body -->
    <div class="smooth-body" >
      <div id="smooth-content">
        <router-view/>
        <index-footer v-if="!isAuthPage"/>
      </div>
    </div>
    <back-to-top/>
  </div>

</template>

<style scoped></style>
