/************ TABLE OF CONTENTS ***************
1	Header Css
1	Header 2 Css
1	Header 3 Css
1	Header 4 Css
1	Hero Css
1	Catagory Css
1	About Css
1	Partner Css
1	Fav Course Css
1	Counter css
1	Cta css
1	Text Slider css
1	Blog css
1	Team css
1	Skill css
1	Footer css
1	Client css
1	Care css
1	Event css
1	Faq css
1	Certificate css
1	Gallery css
1	Course css
1	Student View css
1	How it work css
1	Program css
1	Fees css
1	Login css
1	Shop css
1	Breadcrumb css
1	Course Details css
1	Reboot css
1	Responsive css
    �������أ�https://www.bootstrapmb.com 
**********************************************/
@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
.top-header {
  position: absolute;
  width: 100%;
  z-index: 1050;
  height: 50px;
  top: 0;
  left: 0;
  background-color: #191E24;
  overflow: hidden;
}
.top-header .top-social-icon .header-icon li a span {
  font-size: 15px;
  margin-right: 8px;
}
.top-header .top-social-icon .header-icon li span {
  color: #fff;
  font-family: var(--ff-heading);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.top-serach-bar {
  position: relative;
}
.top-serach-bar .form-control {
  border-radius: 5px;
  background: #F5F5F5;
  border: none;
  position: relative;
  height: 40px;
  width: 100%;
  color: #808287;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.top-serach-bar .search-btn {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  border: none;
  font-size: 14px;
}

.header-icon {
  padding-left: 0;
}
.header-icon li {
  display: inline-block;
  list-style: none;
}

.contact-info a {
  margin-right: 25px;
  color: #EF5C72;
}

.cart-btn-area a {
  font-size: 22px;
}

.cart-badge {
  position: absolute;
  bottom: 0;
  left: -2px;
  background-color: #6956F9;
  color: #fff;
  font-family: var(--ff-heading);
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  line-height: 16px;
  text-align: center;
}

.cate-btn {
  color: #808287;
  font-family: var(--ff-heading);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.cate-icon {
  margin-right: 5px;
}

.contact-desc {
  margin-left: 8px;
  color: #fff;
  font-family: var(--ff-heading);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

@media only screen and (min-width: 768px) {
  .top-header .contact-info a::after {
    position: absolute;
    width: 2px;
    height: 100%;
    background-color: rgba(63, 80, 224, 0.1);
    top: 0;
    right: -21px;
    z-index: 10;
    content: "";
  }
}
.top-header .contact-info a:last-child {
  margin-right: 0;
}

.top-header .contact-info a:last-child::after {
  display: none;
}

.top-header .contact-info a:hover,
.top-header .contact-info a:focus {
  color: #F8941F;
}

.menu-area- {
  padding: 30px 20px;
}

.mobile-ver {
  display: none;
}

.top-social-conatct {
  justify-content: end;
}

.header-area {
  position: absolute;
  width: 100%;
  z-index: 999;
  top: 50px;
  left: 0;
  transition: 350ms ease;
}

.header-area .navbar-toggler {
  padding: 0;
  font-size: 20px;
  color: #191E24;
  width: 2rem;
  height: 2rem;
  line-height: 1;
  background-color: rgba(63, 80, 224, 0.1);
}

.header-area .navbar-toggler i {
  line-height: 1;
}

.header-area .navbar-toggler:focus {
  box-shadow: none;
}

.header-area .navbar-nav-scroll {
  max-height: 50vh;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar-nav-scroll {
    max-height: 90vh;
  }
}
.header-area .navbar {
  transition-duration: 500ms;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar {
    padding-top: 0;
    padding-bottom: 0;
  }
}
.header-area .navbar .navbar-brand {
  padding-top: 0;
  padding-bottom: 0;
  line-height: 1 !important;
}

.header-area .navbar .navbar-brand > img {
  max-height: 1.5rem;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar .navbar-brand > img {
    max-height: 2rem;
  }
}
@media only screen and (min-width: 992px) {
  .header-area .navbar .navbar-brand {
    padding-right: 5px;
  }
}
.header-area .navbar-nav li > a {
  color: #808287;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  padding: 0.375rem 0.75rem;
  text-transform: capitalize;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li > a {
    padding: 1rem 0.03;
  }
}
@media only screen and (min-width: 1200px) {
  .header-area .navbar-nav li > a {
    padding: 1.4rem 1rem;
  }
}
.header-area .navbar-nav li > a:hover,
.header-area .navbar-nav li > a:focus {
  color: #F8941F;
}

.header-area .navbar-nav li ul {
  list-style: none;
  position: relative;
  z-index: 100;
  top: 100%;
  width: 100%;
  border-radius: 0.5rem;
  display: none;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li ul {
    padding: 1rem 0;
    transition: all 300ms ease-in-out;
    position: absolute;
    top: calc(100% + 1rem);
    width: 13rem;
    background-color: #ffffff;
    visibility: hidden;
    border: 1px solid #dee1e6;
    opacity: 0;
    display: block;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px;
  }
}
.header-area .navbar-nav li ul li a {
  transition-duration: 400ms;
  position: relative;
  font-size: 15px;
  padding: 0.02rem 12px;
  color: #808287;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  display: block;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li ul li a {
    padding: 0.1rem 1rem;
  }
}
.header-area.sticky .menu-area- {
  padding: 15px 20px;
}

.header-area .navbar-nav li ul li a:hover,
.header-area .navbar-nav li ul li a:focus {
  background-color: transparent;
  color: #F8941F;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li ul li a:hover,
  .header-area .navbar-nav li ul li a:focus {
    color: #F8941F;
  }
}
.header-area .navbar-nav li.dropdown-list {
  position: relative;
  z-index: 1;
}

.header-area .navbar-nav li.dropdown-list .dropdown-toggler {
  position: absolute;
  width: 100%;
  height: 36px;
  z-index: 100;
  background-color: transparent;
  color: #1f0757;
  top: 2px;
  cursor: pointer;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 24px;
  padding-right: 0.75rem;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li.dropdown-list .dropdown-toggler {
    display: none;
  }
}
.header-area .navbar-nav li.dropdown-list > a {
  position: relative;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li.dropdown-list > a::after {
    position: relative;
    right: 0;
    top: 2px;
    transition: all 500ms;
    content: "\e924";
    font-size: 18px;
    color: #808287;
    display: inline-block;
    font-family: "icomoon" !important;
    margin-left: 0.375rem;
  }
}
@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li.dropdown-list:hover ul,
  .header-area .navbar-nav li.dropdown-list:focus ul {
    visibility: visible;
    opacity: 1;
    top: 100%;
  }
}
.header-area .navbar-nav li.dropdown-list .dropdown-list .dropdown-toggler {
  right: 1rem;
}

.header-area .navbar-nav li.dropdown-list .dropdown-list > a {
  position: relative;
  padding-right: 1.375rem;
  width: 100%;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li.dropdown-list .dropdown-list > a::after {
    position: absolute;
    top: 50%;
    transform: translateY(-50%) rotate(-90deg);
    right: 1.5rem;
    transition: all 400ms;
    content: "\f282";
  }
}
@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li.dropdown-list .dropdown-list > a:hover::after,
  .header-area .navbar-nav li.dropdown-list .dropdown-list > a:focus::after {
    color: #F8941F;
  }
}
.header-area .navbar-nav li.dropdown-list .dropdown-list ul {
  display: none;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li.dropdown-list .dropdown-list ul {
    margin-left: 0.5rem;
    top: 0;
    left: 100%;
    visibility: hidden;
    opacity: 0;
    display: block;
    width: 12rem;
  }
}
@media only screen and (min-width: 992px) {
  .header-area .navbar-nav li.dropdown-list .dropdown-list:hover ul,
  .header-area .navbar-nav li.dropdown-list .dropdown-list:focus ul {
    visibility: visible;
    opacity: 1;
  }
}
.header-area .navbar-nav li:hover > a,
.header-area .navbar-nav li:focus > a {
  color: #F8941F;
}

.header-area .navbar-nav li:hover.dropdown-list > a::after,
.header-area .navbar-nav li:focus.dropdown-list > a::after {
  color: #F8941F;
}

.header-area .navbar-collapse {
  margin-top: 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 0.75rem;
}

@media only screen and (min-width: 992px) {
  .header-area .navbar-collapse {
    margin-top: 0;
    padding-top: 0;
    border: 0;
  }
}
.cart-btn-area a {
  color: #808287;
}

.header-area.sticky {
  position: fixed;
  top: 0;
  background-color: #ffffff;
  border-bottom-color: transparent;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.125);
  z-index: 10101010;
}

.top-dropdown {
  position: relative;
  z-index: 100;
  margin-right: 0.75rem;
}

@media only screen and (min-width: 768px) {
  .top-dropdown {
    margin-right: 15px;
  }
}
.top-dropdown:last-child {
  margin-right: 0;
}

.top-dropdown button {
  position: relative;
  background-color: transparent;
  padding: 0;
  border: 0;
  font-size: 14px;
  color: #1f0757;
  font-weight: 600;
}

.top-dropdown button i {
  margin-right: 0.25rem;
}

.top-dropdown button::after {
  display: none;
}

.top-dropdown .dropdown-menu {
  margin-top: -15px;
  border: 0;
  transition: all 350ms ease-in;
  min-width: 8rem;
  background-color: #fff;
  border-radius: 0.5rem;
  animation-name: dropdownAnimation;
  animation-duration: 350ms;
  animation-fill-mode: both;
  box-shadow: 0 2px 9px rgba(0, 0, 0, 0.125);
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.top-dropdown .dropdown-menu .dropdown-item {
  white-space: normal;
  font-size: 14px;
  color: #808287;
  font-weight: 400;
  display: flex;
  align-items: center;
  padding: 0.25rem 1.5rem;
}

.top-dropdown .dropdown-menu .dropdown-item:hover,
.top-dropdown .dropdown-menu .dropdown-item:focus {
  color: #ffc107;
  background-color: transparent;
}
@keyframes dropdownAnimation {
  from {
    opacity: 0;
    transform: translate3d(0, 55px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 38px, 0);
  }
}
.top-header.home-2 {
  background-color: transparent;
  height: auto;
}
.top-header.home-2 .header-top-home-2 {
  padding: 22px 40px;
}
.top-header.home-2 .header-2-contact-area .header-2-contact-info .contact-info-icon {
  margin-right: 20px;
}
.top-header.home-2 .header-2-contact-area .header-2-contact-info .contact-info-icon span {
  color: #808287;
  font-size: 30px;
}
.top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text p {
  color: #00255C;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text h6 {
  color: #191E24;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}

.mobile-menu {
  display: none;
}

.header-area.home-2 {
  top: 60px;
}
.header-area.home-2 .navbar-nav ul li a {
  color: #808287;
}
.header-area.home-2.sticky {
  top: 0;
}
.header-area.home-2.sticky .navbar-nav li > a {
  color: #808287;
}
.header-area.home-2.sticky .navbar-nav li.dropdown-list > a::after {
  color: #808287;
}
.header-area.home-2 .menu-area- {
  padding: 0 20px;
}
.header-area.home-2 .cart-btn-area a span:before {
  color: #fff;
}
.header-area.home-2 .search-icon {
  margin-right: 15px;
  border-right: 1px solid rgba(255, 255, 255, 0.15);
  padding-right: 15px;
}
.header-area.home-2 .search-icon span:before {
  color: #fff;
}
.header-area.home-2 .cart-search-area {
  background-color: #F8941F;
  padding: 20px;
}
.header-area.home-2 .navbar-nav li > a {
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}
.header-area.home-2 .navbar-nav li.dropdown-list > a::after {
  color: #fff;
}

.search-toggle-open.header-search .search-icon span {
  color: #fff;
  font-size: 20px;
}

.search-toggle-open.header-search .search-icon:hover {
  color: #F8941F;
}

.search-toggle-open.header-search {
  cursor: pointer;
}

.sidebar__toggle {
  height: 38px;
  width: 38px;
  border-radius: 50%;
}

.submit-btn-search {
  border: none;
  background-color: transparent;
}

.bar-icon {
  width: 17px;
  height: 14px;
}

.bar-icon span,
.bar-icon span:nth-child(2) small {
  background: #fff;
}

.sidebar__toggle:hover {
  border: 1px solid #F8941F;
}

.vw-search-input input[type=text] {
  background: transparent;
  color: #191E24;
  border: 2px solid #6956F9;
  border-radius: 10px;
  fill: #808287;
  -webkit-text-fill-color: #808287;
}

.vw-search-input input[type=text]::-moz-placeholder {
  color: #fff;
}

.vw-search-input input[type=text]::placeholder {
  color: #fff;
}

.vw-search-input button {
  color: #fff;
}

.vw-search-input button:hover {
  color: #191E24;
}

.vw-search-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1010101010;
  background-color: #fff;
  padding: 90px 15px;
  visibility: visible;
  opacity: 0;
  transform: translateY(-120%);
}

.vw-search-area.opened {
  transform: translateY(0%);
  visibility: visible;
  opacity: 1;
}

.vw-search-input {
  position: relative;
}

.vw-search-input button {
  position: absolute;
  top: 54%;
  right: 20px;
  transform: translateY(-50%);
  font-size: 24px;
}

.vw-search-input button:hover {
  color: var(--theme-color);
}

.vw-search-category span {
  color: var(--black);
}

.vw-search-category a {
  font-size: 14px;
  margin-left: 5px;
}

.vw-search-category a:hover {
  color: var(--primary-color);
}

.vw-search-close {
  position: absolute;
  top: 15px;
  right: 15px;
}

.vw-search-close-btn {
  border-radius: 50%;
  background: #ECECEC;
  border: 9px solid transparent;
  color: var(--black);
  width: 36px;
  height: 36px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.vw-search-close-btn::after,
.vw-search-close-btn::before {
  content: "";
  position: absolute;
  height: 1px;
  width: 90%;
  top: 46%;
  left: 1px;
  transform-origin: 50% 50%;
  background-color: var(--black);
  opacity: 1;
  transition: transform ease 0.25s;
}

.vw-search-close-btn::before {
  transform: rotate(45deg);
}

.vw-search-close-btn::after {
  transform: rotate(-45deg);
}

.vw-search-close-btn:hover::before {
  transform: rotate(-45deg);
}

.vw-search-close-btn:hover::after {
  transform: rotate(45deg);
}

.body-overlay.opened {
  opacity: 0.7;
  visibility: visible;
}

.body-overlay {
  background-color: #151515;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 999;
  left: 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s 0s ease-out;
}

.search-form-2 {
  width: 100%;
  height: 50px;
  background: transparent;
  color: #191E24;
  padding: 15px;
  border-radius: 10px;
  font-size: 15px;
}

.top-header.three {
  background-color: #fff;
  border-top: 5px solid #6956F9;
  border-bottom: 1px solid #CBDEDE;
  height: 50px;
}
.top-header.three .contact-info a i {
  color: #808287;
  font-size: 14px;
}
.top-header.three .contact-info a .contact-desc-3 {
  color: #191E24;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.top-header.three .top-social-icon.three .header-icon li .desc-f-3 {
  color: #808287;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.top-header.three .top-social-icon.three .header-icon li a i {
  color: #191E24;
  font-size: 13px;
}
.top-header.three .top-social-conatct-3 {
  float: right;
}
.top-header.three .top-social-conatct-3 .header-3-bread {
  padding-left: 0;
}
.top-header.three .top-social-conatct-3 .header-3-bread li {
  display: inline-block;
  list-style: none;
  font-family: var(--ff-heading);
  color: #191E24;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-right: 5px;
}
.top-header.three .top-social-conatct-3 .header-3-bread li:last-child {
  margin-right: 0;
}
.top-header.three .top-social-conatct-3 .header-3-bread li i {
  font-size: 24px;
  color: #191E24;
  line-height: 1;
  position: relative;
  top: 6px;
}

.header-area.three {
  background-color: #fff;
  padding: 25px 0;
}
.header-area.three .cart-search-area.three.desktop {
  display: none !important;
}
.header-area.three .navbar .navbar-brand {
  padding-right: 50px;
}
.header-area.three .cart-search-area.three .search-home-2.three {
  margin-right: 25px;
  padding-right: 25px;
  border-right: 1px solid rgba(0, 0, 0, 0.15);
}
.header-area.three .cart-search-area.three .search-home-2.three .search-icon i {
  color: #191E24;
  font-size: 20px;
  position: relative;
  top: 3px;
}
.header-area.three .cart-search-area .cart-btn-area.three a {
  color: #191E24;
}
.header-area.three .cart-search-area .cart-btn-area.three a i {
  color: #191E24 !important;
  font-size: 20px;
}

.top-header.four {
  position: relative;
  background-color: #fff;
  padding: 5px 0;
  border-bottom: 1px solid #CBDEDE;
  overflow: visible;
}
.top-header.four .lan-btn {
  color: #191E24;
  font-family: var(--ff-heading);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.cart-login--area-4 .cart-btn-area-4 a {
  color: #191E24;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-left: 20px;
}
.cart-login--area-4 .cart-btn-area-4 a i {
  font-size: 12px;
  margin-right: 3px;
}

.top-serach-bar-four {
  position: relative;
}
.top-serach-bar-four .form-control {
  border-radius: 5px;
  background: #F5F5F5;
  border: none;
  position: relative;
  height: 40px;
  width: 370px;
  color: #808287;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.top-serach-bar-four .search-btn {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  border: none;
  font-size: 14px;
}

.header-area.four {
  background-color: #fff;
  padding: 15px 0;
}
.header-area.four .navbar-nav li > a {
  color: #191E24;
}
.header-area.four .navbar-nav li.dropdown-list > a::after {
  color: #191E24;
}
.header-area.four .navbar-nav li > a {
  padding: 0 1.5rem;
}
.header-area.four .navbar .navbar-brand {
  padding-right: 25px;
}

.hero-area {
  height: 900px;
  background-color: #F2F8F8;
  position: relative;
  overflow: hidden;
}
.hero-area .wel-shape-course-title {
  position: absolute;
  top: 15%;
  left: -4%;
  z-index: 1010;
}
.hero-area .wel-shape-course-title .shape-title-course {
  position: absolute;
  top: 44%;
  left: 62%;
  transform: translate(-50%, -50%);
  width: 100%;
  margin-bottom: 0;
  z-index: 22;
}
.hero-area .wel-shape-course-title .shape-title-course p {
  color: #fff;
  font-size: 14px;
  margin-bottom: 0;
}
.hero-area .shape-1 {
  position: absolute;
  top: 15%;
  left: 2%;
  z-index: 1;
}
.hero-area .shape-1 img {
  width: 80%;
}
.hero-area .wel-shape-2 {
  position: absolute;
  right: 3%;
  top: 13%;
  z-index: 1;
}
.hero-area .wel-shape-7 {
  position: absolute;
  right: 3%;
  top: 13%;
}
.hero-area .wel-shape-6 {
  position: absolute;
  bottom: 0;
  right: 0;
}
.hero-area .wel-shape-3 {
  position: absolute;
  right: 4%;
  top: 2%;
  z-index: 1010101;
}
.hero-area .wel-shape-4 {
  position: absolute;
  top: 25%;
  left: -20%;
}
.hero-area .wel-shape-5 {
  position: absolute;
  left: -15%;
  bottom: 0;
  z-index: 9;
}

.hero-content-text {
  margin-top: 120px;
}
.hero-content-text h6 {
  font-size: 24px;
  color: #808287;
  font-weight: 400;
}
.hero-content-text h6 span {
  color: #F8941F;
}

.hero-img-area {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 1;
}
.hero-img-area .hero-img-user {
  position: relative;
  z-index: 1010;
}

.hero-content-text {
  position: relative;
  z-index: 10;
}

.hero-content-text h2 {
  color: #191E24;
  font-size: 70px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  line-height: 94px;
}

.hero-content-text h2 span {
  color: #EF5C72;
  position: relative;
  z-index: 9;
  font-weight: 400;
}

.hero-content-text h2 span::after {
  position: absolute;
  content: url(../img/bg-img/shape-7.png);
  bottom: -41px;
  left: 0;
  z-index: 1;
}

.hero-content-text p {
  line-height: 29px;
  margin-top: 25px;
}

.round-ani {
  transition: all 0.1s 0s ease-out;
  animation: spin 20s linear infinite;
}

.shake-ani {
  animation: shake 20s infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.hero-area-2 {
  height: 1050px;
  position: relative;
  background-color: #F2F8F8;
}
.hero-area-2 .hero-2-curbe-shape {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}
.hero-area-2 .welcome-img-2 img {
  position: relative;
  z-index: 10;
}
.hero-area-2 .shape-box {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}
.hero-area-2 .bag-shape {
  position: absolute;
  left: -11%;
  bottom: -29%;
  z-index: 1;
}
.hero-area-2 .hero-2-shape-1 {
  position: absolute;
  top: 18%;
  left: 8%;
  z-index: 1;
}
.hero-area-2 .hero-2-shape-2 {
  position: absolute;
  bottom: 15%;
  left: 4%;
  z-index: 1;
  animation: wobble-vertical 5s infinite;
}
.hero-area-2 .hero-2-shape-3 {
  position: absolute;
  top: 20%;
  right: 4%;
  z-index: 1;
  animation: swing 7s infinite;
}

.hero-area-3 {
  position: relative;
  z-index: 1;
}

.single-photograph {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 850px;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

.single-photograph::after {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  content: "";
  background-color: rgba(25, 30, 36, 0.5);
  z-index: -10;
}

.single-photograph .photo-title-area {
  padding: 100px 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}
.single-photograph .photo-title-area h6 {
  color: #fff;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.single-photograph .photo-title-area h6 i {
  font-size: 20px;
  color: #F8941F;
  margin-right: 5px;
}
.single-photograph .photo-title-area h2 {
  color: #fff;
  text-align: center;
  font-size: 82px;
  font-style: normal;
  font-weight: 600;
  line-height: 94px;
}
.single-photograph .photo-title-area .wel-btn-3 {
  background-color: #F8941F;
  padding: 10px 20px;
  display: inline-block;
  color: #fff;
  border-radius: 3px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  position: relative;
  overflow: hidden;
  margin-top: 20px;
}
.single-photograph .photo-title-area .wel-btn-3 i {
  margin-right: 3px;
}
.single-photograph .photo-title-area .wel-btn-3::after {
  transition-duration: 400ms;
  position: absolute;
  width: 0;
  height: 100%;
  top: 0;
  content: "";
  background-color: rgba(255, 255, 255, 0.2);
  left: -10%;
}
.single-photograph .photo-title-area .wel-btn-3:hover::after {
  width: 120%;
}

.photography-nav-wrapper {
  position: absolute;
  width: 100%;
  bottom: -40px;
  left: 0;
  right: 0;
  z-index: 1010;
}

.photography-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 0;
}

.photography-nav li {
  cursor: pointer;
  list-style: none;
  margin: 0 0.25rem;
  border-radius: 0.25rem;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  width: 1.5rem;
  height: 1.5rem;
}

@media only screen and (min-width: 576px) {
  .photography-nav li {
    width: 2rem;
    height: 2rem;
  }
}
@media only screen and (min-width: 768px) {
  .photography-nav li {
    margin: 0 0.5rem;
    border-radius: 0.5rem;
    width: 3rem;
    height: 3rem;
  }
}
@media only screen and (min-width: 992px) {
  .photography-nav li {
    margin: 0 0.5rem;
    border-radius: 50%;
    width: 5rem;
    height: 5rem;
    border: 5px solid #fff;
    box-shadow: 0px 4px 30px 0px rgba(193, 171, 171, 0.25);
  }
}
.photography-nav li.tns-nav-active {
  border: 5px solid #6956F9;
}

.photography-nav li:last-child {
  margin-right: 0;
}

.photography-nav li:first-child {
  margin-left: 0;
}

.background-shape .circle1 {
  width: 10%;
  height: 90%;
  background-color: #faf9ff;
  position: absolute;
  top: 0;
  left: 12%;
  z-index: -30;
}

.portfolio-menu button {
  transition-duration: 400ms;
  padding: 0.375rem 1.25rem;
  border: 0;
  margin-right: 1rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  background-color: #ffffff;
  color: #1f0757;
}

.portfolio-menu button:last-child {
  margin-right: 0;
}

.portfolio-menu button:hover {
  background-color: #ffffff;
}

.portfolio-menu button.active {
  color: #ffffff;
  background-color: #003fe0;
}

.portfolio-menu.home3 button {
  padding: 0;
  background-color: transparent;
  color: #1f0757;
  font-size: 1rem;
  margin-right: 1.5rem;
}

.portfolio-menu.home3 button:hover {
  background-color: transparent;
}

.portfolio-menu.home3 button.active {
  color: #d63384;
  background-color: transparent;
}

.portfolio-card {
  position: relative;
  z-index: 1;
}

.portfolio-card .portfolio-image {
  overflow: hidden;
  position: relative;
  border-radius: 0.75rem 0.75rem 0 0;
  min-height: 5rem;
}

.portfolio-card .portfolio-image img {
  transition: all 300ms ease;
  border-radius: 0.75rem 0.75rem 0 0;
}

.portfolio-card .portfolio-image .overlay-content {
  transition-duration: 400ms;
  border-radius: 0.75rem 0.75rem 0 0;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(31, 7, 87, 0.6);
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  padding: 1.5rem;
}

.portfolio-card .portfolio-image .overlay-content a {
  display: block;
  color: #ffffff;
  font-size: 2rem;
}

.portfolio-card .description {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background-color: #ffffff;
  border-radius: 0 0 0.75rem 0.75rem;
}

.portfolio-card .description a {
  display: block;
  font-size: 1rem;
  color: #1f0757;
}

.portfolio-card .description a:hover,
.portfolio-card .description a:focus {
  color: #003fe0;
}

.portfolio-card .description p {
  font-size: 14px;
  font-weight: 600;
  padding: 0.25rem 0.625rem;
  border: 2px solid #dee1e6;
  line-height: 1;
  border-radius: 0.25rem;
}

.portfolio-card:hover .portfolio-image img,
.portfolio-card:focus .portfolio-image img {
  transform: scale(1.1);
}

.portfolio-card:hover .overlay-content,
.portfolio-card:focus .overlay-content {
  opacity: 1;
  visibility: visible;
}

.hero-area-4 {
  height: 850px;
  position: relative;
  overflow: hidden;
}
.hero-area-4 .wel-shape-1 {
  position: absolute;
  right: -2%;
  bottom: -45%;
}
.hero-area-4 .hero-content-text h2 {
  color: #fff;
  font-size: 60px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.hero-area-4 .hero-content-text p {
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-top: 15px;
}

.popular-cata-area {
  padding-top: 140px;
}
.popular-cata-area .map-img {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.popular-cata-area .single-cate-card {
  border-radius: 5px;
  background: #fff;
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.07);
  padding: 22px;
  transition-duration: 600ms;
}
.popular-cata-area .single-cate-card:hover {
  transform: translateY(-20px);
}
.popular-cata-area .single-cate-card .single-cate-content {
  border-radius: 5px;
  border: 1px solid #DAE6E6;
  padding: 40px 30px;
}
.popular-cata-area .single-cate-card .single-cate-content .cate-shape {
  position: absolute;
  top: 0;
  right: 0;
}
.popular-cata-area .single-cate-card .single-cate-content .cate-icon {
  height: 100px;
  width: 100px;
  border-radius: 50%;
  margin: auto;
  font-size: 40px;
  text-align: center;
}
.popular-cata-area .single-cate-card .single-cate-content .cate-icon span {
  line-height: 100px;
  color: #fff;
}
.popular-cata-area .single-cate-card .single-cate-content h4 {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-top: 15px;
}
.popular-cata-area .single-cate-card .single-cate-content p {
  color: #808287;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.popular-categories-slider-area .popular-categories-slider {
  padding-top: 100px;
  padding-bottom: 200px;
}
.popular-categories-slider-area .cate-slider-area {
  transform: translateY(-48%);
  position: relative;
}
.popular-categories-slider-area .cate-slider-area .owl-dots {
  width: 100%;
  text-align: center;
  margin-top: 40px;
}
.popular-categories-slider-area .cate-slider-area button.owl-dot {
  background-color: #E0E0E0;
  height: 10px;
  width: 10px;
  margin: 5px;
  border-radius: 50%;
  transition-duration: 700ms;
  position: relative;
}
.popular-categories-slider-area .cate-slider-area button.owl-dot.active {
  background-color: #808287;
  border: 1px solid #808287;
  position: relative;
}
.popular-categories-slider-area .cate-slider-area button.owl-dot.active::after {
  position: absolute;
  content: "";
  height: 15px;
  width: 15px;
  border: 1px solid #808287;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.cate-single-slier {
  background-color: #fff;
  padding: 20px;
  box-shadow: 0px 4px 30px 0px rgba(245, 233, 233, 0.25);
  overflow: hidden;
}
.cate-single-slier .cate-badge {
  position: absolute;
  top: 15px;
  left: 15px;
}
.cate-single-slier .cate-badge p {
  border-radius: 25px;
  color: #fff;
  padding: 5px 20px;
  display: inline-block;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.cate-single-slier .price-tag {
  padding: 5px 20px;
  display: inline-block;
  border-radius: 25px;
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  position: absolute;
  bottom: -13px;
  right: 15px;
  z-index: 2;
  margin-bottom: 0;
  transition-duration: 400ms;
  opacity: 0;
}
.cate-single-slier .cate-meta-info {
  margin-top: 30px;
}
.cate-single-slier .cate-meta-info p {
  display: flex;
  align-items: center;
  margin-right: 25px;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.cate-single-slier .cate-meta-info p:last-child {
  margin-right: 0;
}
.cate-single-slier .cate-meta-info p span {
  font-size: 20px;
  margin-right: 10px;
}
.cate-single-slier h3 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 18px;
}
.cate-single-slier h3 a {
  color: #191E24;
  transition-duration: 400ms;
}
.cate-single-slier h3:hover a {
  color: #F8941F;
}
.cate-single-slier .cate-footer {
  padding-top: 15px;
  border-top: 1px solid #F4E7E7;
}
.cate-single-slier .cate-footer p {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.cate-single-slier .cate-footer p span {
  margin-right: 4px;
}
.cate-single-slier .cate-img img {
  transition: 500ms;
}
.cate-single-slier:hover .price-tag {
  opacity: 1;
}

.popular-cate-2-area {
  padding-top: 120px;
  padding-bottom: 120px;
}
.popular-cate-2-area.pricing-page {
  padding-top: 190px;
  padding-bottom: 380px;
}
.popular-cate-2-area .cate-2-shape {
  position: absolute;
  right: 5%;
  bottom: 0;
}
.popular-cate-2-area .cate-price-card {
  padding: 40px 40px;
  border-radius: 18px;
  position: relative;
  z-index: 9;
  background-color: #6956F9;
}
.popular-cate-2-area .cate-price-card::after {
  position: absolute;
  content: "";
  border-radius: 10px;
  background: rgba(105, 86, 249, 0.3);
  height: 100%;
  width: 100%;
  bottom: -10px;
  right: -10px;
  z-index: 1;
}
.popular-cate-2-area .cate-price-card.two {
  background-color: #F8941F;
  z-index: 99;
}
.popular-cate-2-area .cate-price-card.two::after {
  position: absolute;
  content: "";
  border-radius: 10px;
  background: rgba(248, 148, 31, 0.3);
  height: 100%;
  width: 100%;
  bottom: -10px;
  right: -10px;
  z-index: 1;
}
.popular-cate-2-area .cate-price-card.three {
  background-color: #EF5C72;
  z-index: 99;
}
.popular-cate-2-area .cate-price-card.three::after {
  position: absolute;
  content: "";
  border-radius: 10px;
  background: rgba(239, 92, 114, 0.3);
  height: 100%;
  width: 100%;
  bottom: -10px;
  right: -10px;
  z-index: 1;
}
.popular-cate-2-area .cate-price-card .cate-price-body {
  position: relative;
  z-index: 9;
}
.popular-cate-2-area .cate-price-card .cate-price-body h2 {
  color: #fff;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  position: relative;
  z-index: 9;
  margin-bottom: 0;
}
.popular-cate-2-area .cate-price-card .cate-price-body h2 .cate-price-title {
  color: #fff;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}
.popular-cate-2-area .cate-price-card .cate-price-body h2 .month-desc {
  color: #fff;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.popular-cate-2-area .cate-price-card .cate-price-body h6 {
  color: #fff;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 15px;
  margin-top: 0;
}
.popular-cate-2-area .cate-price-card .cate-price-body ul {
  padding-left: 0;
}
.popular-cate-2-area .cate-price-card .cate-price-body ul li {
  list-style: none;
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 35px;
}
.popular-cate-2-area .cate-price-card .cate-price-body .price-btn-area {
  margin-top: 25px;
}

.popular-cate-area-3 {
  padding-bottom: 110px;
}
.popular-cate-area-3 .single-cate-card-3 {
  border-radius: 5px;
  background: #fff;
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.07);
  padding: 20px;
  margin-bottom: 30px;
  transition-duration: 500ms;
}
.popular-cate-area-3 .single-cate-card-3 .cate-card-img-text-area {
  border: 1px solid #DAE6E6;
  border-radius: 5px;
  padding: 15px;
  padding-bottom: 30px;
}
.popular-cate-area-3 .single-cate-card-3 .cate-card-img-text-area .cate-image-icon-3 {
  position: relative;
}
.popular-cate-area-3 .single-cate-card-3 .cate-card-img-text-area .cate-image-icon-3 .cate-card-3-img {
  position: relative;
  border-radius: 5px;
  background: #F7F4F4;
}
.popular-cate-area-3 .single-cate-card-3 .cate-card-img-text-area .cate-image-icon-3 .cate-card-3-img img {
  height: 150px;
  -o-object-fit: cover;
     object-fit: cover;
}
.popular-cate-area-3 .single-cate-card-3 .cate-card-img-text-area .cate-image-icon-3 .cate-icon-3 {
  position: absolute;
  bottom: -28px;
  left: 50%;
  transform: translateX(-50%);
  height: 66px;
  width: 66px;
  line-height: 66px;
  background-color: #191E24;
  border-radius: 50%;
  text-align: center;
  font-size: 28px;
  transition-duration: 500ms;
}
.popular-cate-area-3 .single-cate-card-3 .cate-card-img-text-area .cate-image-icon-3 .cate-icon-3 i {
  color: #fff;
  line-height: 66px;
}
.popular-cate-area-3 .single-cate-card-3 .cate-card-img-text-area h4 {
  color: #191E24;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-top: 50px;
  margin-bottom: 0;
}
.popular-cate-area-3 .single-cate-card-3:hover {
  transform: translateY(-10px);
}
.popular-cate-area-3 .single-cate-card-3:hover .cate-image-icon-3 .cate-icon-3 {
  background-color: #F8941F;
}

.top-categories-area-4 {
  padding-top: 140px;
  padding-bottom: 110px;
}
.top-categories-area-4 .cate-single-card-4 {
  border-radius: 5px;
  background: #F5F5F5;
  padding: 15px;
  overflow: hidden;
  margin-bottom: 30px;
}
.top-categories-area-4 .cate-single-card-4 .hover-bg-img {
  position: absolute;
  top: 0;
  left: -120%;
  height: 100%;
  width: 100%;
  transition-duration: 500ms;
}
.top-categories-area-4 .cate-single-card-4 .hover-bg-img img {
  width: 100%;
  height: 100%;
}
.top-categories-area-4 .cate-single-card-4 .cate-content-text-4 {
  position: relative;
  z-index: 10;
}
.top-categories-area-4 .cate-single-card-4 .cate-content-text-4 .cate-icon-4 {
  background-color: #E0E0E0;
  height: 54px;
  flex: 0 0 54px;
  width: 54px;
  border-radius: 50%;
  text-align: center;
  margin-right: 20px;
  transition-duration: 500ms;
}
.top-categories-area-4 .cate-single-card-4 .cate-content-text-4 .cate-icon-4 i {
  line-height: 54px;
  font-size: 25px;
}
.top-categories-area-4 .cate-single-card-4 .cate-content-text-4 h3 {
  color: #191E24;
  margin-bottom: 0;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  transition-duration: 500ms;
}
.top-categories-area-4 .cate-single-card-4:hover .hover-bg-img {
  left: 0;
}
.top-categories-area-4 .cate-single-card-4:hover .cate-content-text-4 h3 {
  color: #fff;
}
.top-categories-area-4 .cate-single-card-4:hover .cate-content-text-4 .cate-icon-4 {
  background-color: #F8941F;
}
.top-categories-area-4 .cate-single-card-4:hover .cate-content-text-4 .cate-icon-4 i {
  color: #fff;
}

.student-tab-area .student-tab-heading {
  margin-bottom: 50px;
}
.student-tab-area .nav-pills .nav-link {
  font-family: var(--ff-heading);
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 0 30px;
  color: #808287;
  font-size: 20px;
}
.student-tab-area .nav-pills .nav-link.active,
.student-tab-area .nav-pills .show > .nav-link {
  background-color: transparent;
  color: #6956F9;
  border: none;
  border-radius: 0;
  position: relative;
}
.student-tab-area .nav-pills .nav-link.active::after,
.student-tab-area .nav-pills .show > .nav-link::after {
  position: absolute;
  content: "";
  bottom: -13px;
  left: 0;
  background-color: #6956F9;
  width: 100%;
  height: 1px;
}

.about-us-area {
  position: relative;
  padding-top: 140px;
}
.about-us-area .about-shape-1 {
  position: absolute;
  top: 10%;
  left: 2%;
  z-index: -1;
  animation: swing 7s infinite;
}
.about-us-area .img-badge-text {
  margin-top: 20px;
}
.about-us-area .img-badge-text .img-badge a img {
  width: 50px;
  height: 50px;
  flex: 0 0 50px;
}
.about-us-area .img-badge-text .img-badge-2 a img {
  width: 40px;
  height: 40px;
  flex: 0 0 40px;
}
.about-us-area .about-shape-2 {
  position: absolute;
  bottom: 0;
  left: 1%;
  z-index: -1;
}
.about-us-area .about-shape-3 {
  position: absolute;
  bottom: 0;
  right: 2%;
  z-index: -1;
}
.about-us-area .about-us-title-img h2 {
  font-size: 48px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
  margin-bottom: 35px;
}
.about-us-area .about-us-title-img .about-sub-title {
  display: flex;
  align-items: center;
}
.about-us-area .about-us-title-img .about-sub-title span {
  background-color: #F8941F;
  height: 50px;
  width: 50px;
  display: inline-block;
  line-height: 50px;
  border-radius: 50px;
  font-size: 25px;
  text-align: center;
  margin-right: 15px;
  color: #fff;
}
.about-us-area .about-us-title-img .img-badge-text .img-badge-2 {
  position: relative;
  left: -20px;
}
.about-us-area .about-us-title-img .img-badge-text p {
  color: #191E24;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.about-us-area .about-us-title-img .img-badge-text p a {
  color: #191E24;
  text-decoration: underline;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  transition-duration: 400ms;
}
.about-us-area .about-us-title-img .img-badge-text p a:hover {
  color: #F8941F;
}
.about-us-area .about-us-title-img .about-img {
  margin-top: 115px;
  position: relative;
}
.about-us-area .about-us-title-img .about-img .about-img-1 {
  position: relative;
  z-index: 9;
  border-radius: 10px;
  border: 10px solid #FFF;
  background: lightgray 50%/cover no-repeat;
}
.about-us-area .about-us-title-img .about-img .about-img-2 {
  position: absolute;
  top: -85px;
  right: 0;
}
.about-us-area .about-desc-info-area h6 {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 15px;
}
.about-us-area .about-desc-info-area p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 45px;
}
.about-us-area .about-desc-info-area .about-content-card-area {
  position: relative;
}
.about-us-area .about-desc-info-area .about-content-card-area .about-single-card {
  margin-bottom: 50px;
}
.about-us-area .about-desc-info-area .about-content-card-area .about-single-card .about-card-icon span {
  font-size: 40px;
}
.about-us-area .about-desc-info-area .about-content-card-area .about-single-card h4 {
  margin-top: 12px;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.about-us-area .about-desc-info-area .about-content-card-area .about-single-card p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  /* 181.25% */
  margin-bottom: 0;
}
.about-us-area .about-desc-info-area .about-content-card-area .about-single-card .about-line-shape {
  background: #808287;
  height: 1px;
  width: 51px;
  display: inline-block;
  transition-duration: 500ms;
}
.about-us-area .about-desc-info-area .about-content-card-area .about-single-card:hover .about-line-shape {
  background-color: #F8941F;
  width: 173px;
}

.about-us-area.single-page .about-single-image {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
}
.about-us-area.single-page .about-single-image .about-single-1-img img {
  border-radius: 10px;
}
.about-us-area.single-page .about-single-image .about-single-2-img img {
  border-radius: 10px;
}
.about-us-area.single-page .about-single-image .about-single-2-img .about-info-img-2 {
  margin-top: 25px;
}

.text-slider-section.single-page {
  padding-top: 180px;
}

.counter-up-area.single-page {
  padding-bottom: 140px;
}

.how-we-work-area {
  padding-top: 140px;
  padding-bottom: 140px;
}
.how-we-work-area .how-we-content .how-sub-title {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.how-we-work-area .how-we-content .how-sub-title span {
  background-color: #F8941F;
  height: 50px;
  width: 50px;
  display: inline-block;
  line-height: 50px;
  border-radius: 50px;
  font-size: 25px;
  text-align: center;
  margin-right: 15px;
  color: #fff;
}
.how-we-work-area .how-we-content h2 {
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
  margin-bottom: 15px;
}
.how-we-work-area .how-we-content p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
}
.how-we-work-area .how-it-work-content-3.single-page .play-btn {
  height: 100px;
  width: 100px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #F8941F;
}
.how-we-work-area .how-it-work-content-3.single-page .play-btn .video_player_btn {
  line-height: 100px;
}
.how-we-work-area .how-it-work-content-3.single-page .play-btn .video_player_btn a {
  font-size: 28px;
  color: #fff;
}
.how-we-work-area .how-it-work-content-3.single-page .play-btn::after {
  display: none;
}

.team-area.single-page {
  padding-top: 0;
  padding-bottom: 140px;
}

.partner-area {
  padding-top: 90px;
  padding-bottom: 140px;
}

.partner-area-2 {
  padding: 100px 0;
  padding-bottom: 0;
}
.partner-area-2 .single-slider a {
  text-align: center;
}
.partner-area-2 .single-slider a img {
  max-width: 300px;
}

.favorite-course-area {
  padding-top: 140px;
  padding-bottom: 140px;
}
.favorite-course-area .fav-shape-1 {
  position: absolute;
  top: 10%;
  left: 2%;
}
.favorite-course-area .single-fav-course {
  text-align: center;
  position: relative;
}
.favorite-course-area .single-fav-course .fav-hover-img {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transition-duration: 500ms;
  width: 100%;
}
.favorite-course-area .single-fav-course .fav-hover-img img {
  width: 100%;
}
.favorite-course-area .single-fav-course .fav-icon-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.favorite-course-area .single-fav-course .fav-icon-text h4 {
  margin-top: 20px;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.favorite-course-area .single-fav-course .fav-icon-text p {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.favorite-course-area .single-fav-course:hover .fav-hover-img {
  opacity: 1;
}
.favorite-course-area .single-fav-course:hover h4 {
  color: #fff;
}
.favorite-course-area .single-fav-course:hover p {
  color: #fff;
}

.counter-up-area .row > * {
  padding-right: 30px;
  padding-left: 30px;
}

.single-counter-card {
  text-align: center;
}
.single-counter-card .counter-info {
  border-radius: 165px 165px 0px 165px;
  padding: 72px 55px;
  transition: all 500ms ease;
}
.single-counter-card .counter-info h2 {
  color: #fff;
  font-size: 84px;
  font-style: normal;
  font-weight: 600;
  line-height: 94px;
  margin-bottom: 0;
}
.single-counter-card .counter-info p {
  font-family: var(--ff-heading);
  color: #fff;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.single-counter-card:hover .counter-info {
  border-radius: 165px 0 165px 165px;
}

.counter-upaarea-2 {
  padding-top: 120px;
  padding-bottom: 120px;
  background-color: #6956F9;
  position: relative;
  overflow: hidden;
}
.counter-upaarea-2::after {
  position: absolute;
  content: "";
  background-color: #fff;
  height: 175%;
  width: 220px;
  top: -106px;
  right: -109px;
  z-index: 10;
  transform: rotate(-30deg);
}
.counter-upaarea-2 .counter-shape-3 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.counter-upaarea-2 .counter-shape-2 {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 101;
}
.counter-upaarea-2 .single-counter-2-card {
  position: relative;
  z-index: 10;
}
.counter-upaarea-2 .single-counter-2-card .counter-2-icon {
  flex: 0 0 130px;
  height: 130px;
  width: 130px;
  line-height: 130px;
  border-radius: 50%;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  background-color: rgba(217, 217, 217, 0.3);
  text-align: center;
  margin-right: 30px;
}
.counter-upaarea-2 .single-counter-2-card .counter-2-icon span {
  font-size: 42px;
  line-height: 130px;
  color: #fff;
}
.counter-upaarea-2 .single-counter-2-card .counter-2-text h2 {
  color: #fff;
  color: #FFF;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.counter-upaarea-2 .single-counter-2-card .counter-2-text p {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}

.counter-area-home-2 {
  padding-top: 70px;
  padding-bottom: 70px;
  background-color: #6956F9;
  position: relative;
  overflow: hidden;
  margin-right: 4%;
  margin-left: 4%;
  border-radius: 15px;
  position: relative;
  transform: translateY(140px);
  z-index: 1010;
}
.counter-area-home-2 .counter-shape {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.counter-area-home-2 .single-counter-2-card {
  position: relative;
  z-index: 99;
}
.counter-area-home-2 .single-counter-2-card .counter-2-icon {
  flex: 0 0 130px;
  height: 130px;
  width: 130px;
  line-height: 130px;
  border-radius: 50%;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  background-color: rgba(217, 217, 217, 0.3);
  text-align: center;
  margin-right: 30px;
}
.counter-area-home-2 .single-counter-2-card .counter-2-icon span {
  font-size: 42px;
  line-height: 130px;
  color: #fff;
}
.counter-area-home-2 .counter-2-text h2 {
  color: #fff;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.counter-area-home-2 .counter-2-text p {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}

.counter-upaarea-4 {
  padding-top: 200px;
  padding-bottom: 200px;
}
.counter-upaarea-4 .single-counter-4-card .counter-4-icon {
  flex: 0 0 130px;
  height: 130px;
  width: 130px;
  line-height: 130px;
  border-radius: 50%;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  background-color: rgba(217, 217, 217, 0.3);
  text-align: center;
  margin-right: 30px;
}
.counter-upaarea-4 .single-counter-4-card .counter-4-icon span {
  font-size: 42px;
  line-height: 130px;
  color: #fff;
}
.counter-upaarea-4 .single-counter-4-card .counter-4-text h2 {
  color: #fff;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.counter-upaarea-4 .single-counter-4-card .counter-4-text p {
  margin-bottom: 0;
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.knowledge-cta-area {
  background-color: #F3F9F9;
  z-index: 9;
  padding-top: 140px;
  padding-bottom: 140px;
}
.knowledge-cta-area .cta-shape {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}
.knowledge-cta-area .knowledge-cta-content-img {
  display: flex;
  align-items: end;
  padding: 0 3%;
  flex-wrap: wrap;
}
.knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-img {
  flex: 0 0 35%;
  margin-right: 5%;
  animation: wobble-vertical 5s infinite;
}
@keyframes wobble-vertical {
  16.65% {
    transform: translateY(8px);
  }
  33.3% {
    transform: translateY(-6px);
  }
  49.95% {
    transform: translateY(4px);
  }
  66.6% {
    transform: translateY(-2px);
  }
  83.25% {
    transform: translateY(1px);
  }
  100% {
    transform: translateY(0);
  }
}
.knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text {
  flex: 0 0 50%;
  width: 50%;
  margin-right: 10%;
}
.knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text h2 {
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
}
.knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}
.knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text .course-about-meta {
  padding-left: 0;
  margin-bottom: 30px;
}
.knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text .course-about-meta li {
  margin-bottom: 10px;
  color: #191E24;
  font-family: var(--ff-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  list-style: none;
}
.knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text .course-about-meta li span {
  margin-right: 10px;
}
.knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text .course-about-meta li:last-child {
  margin-bottom: 0;
}

.knowledge-cta-area-2 {
  position: relative;
  padding-top: 250px;
  padding-bottom: 200px;
}
.knowledge-cta-area-2 .knowledge-per-img {
  position: absolute;
  left: 8%;
  bottom: 10%;
  z-index: 1;
}
.knowledge-cta-area-2 .curbe-shape-cta-1 {
  position: absolute;
  top: 0;
  left: 0;
}
.knowledge-cta-area-2 .curbe-shape-cta-2 {
  position: absolute;
  bottom: -1px;
  left: 0;
}
.knowledge-cta-area-2 .knowledge-image {
  text-align: center;
  position: relative;
  z-index: 10;
}
.knowledge-cta-area-2 .knowledge-image img {
  border-radius: 50%;
  height: 350px;
  width: 350px;
  padding: 20px;
  position: relative;
}
.knowledge-cta-area-2 .knowledge-image::after {
  position: absolute;
  content: "";
  top: 0%;
  left: 22%;
  border: 10px dashed #fff;
  z-index: 99;
  height: 350px;
  width: 350px;
  border-radius: 50%;
  transition: all 0.1s 0s ease-out;
  animation: spin 60s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.knowledge-cta-area-2 .knowledge-cta-content h2 {
  color: #fff;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  text-transform: capitalize;
  margin-bottom: 30px;
}
.knowledge-cta-area-2 .knowledge-cta-content .skill-program-card:first-child {
  margin-right: 90px;
}
.knowledge-cta-area-2 .knowledge-cta-content .skill-program-card .program-icon {
  margin-right: 19px;
}
.knowledge-cta-area-2 .knowledge-cta-content .skill-program-card .program-icon span {
  font-size: 50px;
  color: #fff;
}
.knowledge-cta-area-2 .knowledge-cta-content .skill-program-card .skill-program-text h4 {
  color: #fff;
  font-size: 24px;
  margin-bottom: 0;
}

.text-slider-section {
  margin-bottom: 100px;
  position: relative;
  margin-top: -150px;
  overflow: hidden;
}
.text-slider-section .text-slider-box {
  display: flex;
  position: relative;
  animation: aspro-scroller-reverse 90s linear infinite;
  will-change: transform;
  white-space: nowrap;
  gap: 55px;
  left: -40%;
}
.text-slider-section .text-slider-box .slide-box h2 {
  text-transform: capitalize;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #BDBDBD;
  color: transparent;
  font-size: 100px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
@keyframes aspro-scroller-reverse {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(100%, 0);
  }
}

.blog-area {
  padding-top: 140px;
  padding-bottom: 140px;
}
.blog-area.single-page-blog .single-blog-artical {
  margin-bottom: 50px;
}
.blog-area .single-blog-artical .single-blog-card {
  border-radius: 10px;
  overflow: hidden;
}
.blog-area .single-blog-artical .single-blog-card .blog-img {
  overflow: hidden;
}
.blog-area .single-blog-artical .single-blog-card .blog-img img {
  border-radius: 10px;
  width: 100%;
  transition: 1200ms;
}
.blog-area .single-blog-artical .single-blog-card:hover .blog-img img {
  transform: scale(1.09);
}
.blog-area .single-blog-artical .single-blog-card .date-badge {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #F8941F;
  height: 112px;
  width: 112px;
  border-radius: 50%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}
.blog-area .single-blog-artical .single-blog-card .date-badge h5 {
  color: #fff;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.blog-area .single-blog-artical .single-blog-card .date-badge p {
  color: #fff;
  font-family: var(--ff-heading);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-transform: uppercase;
  margin-bottom: 0;
}
.blog-area .single-blog-artical .single-blog-card .blog-content-text {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 9;
  padding: 30px;
  transition-duration: 600ms;
  padding-top: 100px;
}
.blog-area .single-blog-artical .single-blog-card .blog-content-text::after {
  content: "";
  position: absolute;
  bottom: -32%;
  width: 50%;
  height: 110%;
  background: rgba(239, 92, 114, 0.8);
  transform: skewY(-18deg);
  z-index: -1;
  left: 0;
  transform-origin: top left;
}
.blog-area .single-blog-artical .single-blog-card .blog-content-text::before {
  content: "";
  position: absolute;
  bottom: -32%;
  width: 50%;
  height: 110%;
  background: rgba(239, 92, 114, 0.8);
  transform: skewY(18deg);
  z-index: -1;
  right: 0;
  transform-origin: top right;
}
.blog-area .single-blog-artical .single-blog-card .blog-content-text .blog-header p {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin: 0 15px;
}
.blog-area .single-blog-artical .single-blog-card .blog-content-text .blog-header p span {
  margin-right: 3px;
}
.blog-area .single-blog-artical .single-blog-card .blog-content-text h3 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-top: 15px;
  position: relative;
  z-index: 99;
}
.blog-area .single-blog-artical .single-blog-card .blog-content-text h3 a {
  color: #fff;
}
.blog-area .single-blog-artical .single-blog-card .hover-blog-content-img {
  position: absolute;
  bottom: -500px;
  left: 0;
  z-index: 99;
  padding: 30px;
  padding-bottom: 45px;
  transition-duration: 800ms;
  padding-top: 100px;
}
.blog-area .single-blog-artical .single-blog-card .hover-blog-content-img::after {
  content: "";
  position: absolute;
  bottom: -31%;
  width: 50%;
  height: 110%;
  background: rgba(25, 30, 36, 0.8);
  -webkit-backdrop-filter: blur(6.3499999046px);
          backdrop-filter: blur(6.3499999046px);
  transform: skewY(-18deg);
  z-index: -1;
  left: 0;
  transform-origin: top left;
}
.blog-area .single-blog-artical .single-blog-card .hover-blog-content-img::before {
  content: "";
  position: absolute;
  bottom: -31%;
  width: 50%;
  height: 110%;
  background: rgba(25, 30, 36, 0.8);
  -webkit-backdrop-filter: blur(6.3499999046px);
          backdrop-filter: blur(6.3499999046px);
  transform: skewY(18deg);
  z-index: -1;
  right: 0;
  transform-origin: top right;
}
.blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content {
  position: relative;
  z-index: 99999;
}
.blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content .blog-header p {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin: 0 15px;
}
.blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content .blog-header p span {
  margin-right: 3px;
}
.blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content h3 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-top: 15px;
}
.blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content h3 a {
  color: #fff;
}
.blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content p {
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
}
.blog-area .single-blog-artical .blog-btn-area {
  position: absolute;
  bottom: -27px;
  left: 50%;
  z-index: 999;
  transform: translateX(-50%);
  opacity: 0;
  transition-duration: 1000ms;
}
.blog-area .single-blog-artical .blog-btn-area .blog-btn {
  background-color: #fff;
  height: 52px;
  width: 52px;
  border-radius: 50%;
  text-align: center;
  line-height: 52px;
  display: inline-block;
  box-shadow: 0px 4px 42px rgba(0, 0, 0, 0.25);
  color: #F8941F;
  transition-duration: 500ms;
}
.blog-area .single-blog-artical .blog-btn-area .blog-btn:hover {
  background-color: #F8941F;
  color: #fff;
}
.blog-area .single-blog-artical:hover .single-blog-card .blog-content-text {
  opacity: 1;
}
.blog-area .single-blog-artical:hover .single-blog-card .hover-blog-content-img {
  bottom: 0;
}
.blog-area .single-blog-artical:hover .blog-btn-area {
  opacity: 1;
}
.blog-area .single-blog-artical:hover .deft-blog-content-info .blog-content-text {
  opacity: 0;
}

.blog-area-home-2 {
  padding-bottom: 140px;
  padding-top: 140px;
}
.blog-area-home-2 .blog-shape-1 {
  position: absolute;
  right: 15%;
  top: 0;
  transform: rotate(16deg);
  z-index: 1;
}

.blog-card-home-2 {
  border-radius: 10px;
  border: 2px dashed #F8941F;
  background: #FFF;
  padding: 25px 15px;
  position: relative;
  z-index: 10;
}
.blog-card-home-2.home-3 {
  padding: 25px 20px;
}
.blog-card-home-2 .blog-image-2 img {
  width: 100%;
  border-radius: 10px;
}
.blog-card-home-2 .blog-image-2 .date-badge {
  position: absolute;
  top: 30px;
  left: 30px;
  background-color: #F8941F;
  height: 112px;
  width: 112px;
  border-radius: 50%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}
.blog-card-home-2 .blog-image-2 .date-badge h5 {
  color: #fff;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.blog-card-home-2 .blog-image-2 .date-badge p {
  color: #fff;
  font-family: var(--ff-heading);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-transform: uppercase;
  margin-bottom: 0;
}
.blog-card-home-2 .blog-conetnt-card-2 {
  background: rgba(239, 92, 114, 0.8);
  text-align: center;
  padding: 30px 50px;
}
.blog-card-home-2 .blog-conetnt-card-2.home-3 {
  padding: 30px 120px;
}
.blog-card-home-2 .blog-conetnt-card-2 .blog-header {
  margin-bottom: 15px;
}
.blog-card-home-2 .blog-conetnt-card-2 .blog-header p {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin: 0 15px;
  text-transform: capitalize;
}
.blog-card-home-2 .blog-conetnt-card-2 h3 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.blog-card-home-2 .blog-conetnt-card-2 h3 a {
  color: #fff;
}
.blog-card-home-2 .blog-conetnt-card-2 {
  background: rgba(239, 92, 114, 0.8);
  -webkit-backdrop-filter: blur(6.3499999046px);
          backdrop-filter: blur(6.3499999046px);
  text-align: center;
  padding: 30px 50px;
  position: absolute;
  bottom: 15px;
  left: 15px;
  right: 15px;
  border-radius: 10px;
}

.blog-area-3 {
  background-color: #F3F9F9;
  padding-top: 140px;
  padding-bottom: 140px;
  position: relative;
}
.blog-area-3 .blog-shape-curbe-1 {
  position: absolute;
  top: 0;
  left: 0;
}
.blog-area-3 .blog-shape-2 {
  position: absolute;
  bottom: 0;
  right: 0;
}
.blog-area-3 .blog-card-news-3 {
  border-top: 2px dashed #F8941F;
  padding-top: 35px;
  margin-top: 35px;
}
.blog-area-3 .blog-card-news-3:first-child {
  margin-top: 0;
}
.blog-area-3 .blog-card-news-3:last-child {
  border-bottom: 2px dashed #F8941F;
  padding-bottom: 35px;
}
.blog-area-3 .blog-card-news-3 .news-img-3 {
  flex: 0 0 120px;
  width: 120px;
  border-radius: 10px;
  border: 2px dashed #F8941F;
  background: #FFF;
  margin-right: 20px;
}
.blog-area-3 .blog-card-news-3 .news-img-3 img {
  border-radius: 10px;
  padding: 5px;
}
.blog-area-3 .blog-card-news-3 .news-content-3 h4 {
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.blog-area-3 .blog-card-news-3 .news-content-3 h4 a {
  color: #191E24;
  transition-duration: 400ms;
}
.blog-area-3 .blog-card-news-3 .news-content-3 h4:hover a {
  color: #F8941F;
}
.blog-area-3 .blog-card-news-3 .news-content-3 .news-f-user-info-3 p {
  margin-bottom: 0;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-right: 30px;
  text-transform: capitalize;
}

.course-details-content-area .blog-details-header {
  margin-top: 50px;
}
.course-details-content-area .blog-details-header p {
  color: #191E24;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-right: 25px;
  text-transform: capitalize;
}
.course-details-content-area .blog-details-header p i {
  margin-right: 3px;
}
.course-details-content-area p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 30px;
}
.course-details-content-area .block-card {
  border-radius: 20px;
  border: 1px dashed #6956F9;
  background: #fff;
  box-shadow: 0px 4px 40px 0px rgba(221, 203, 203, 0.25);
  padding: 40px 50px;
  margin-bottom: 40px;
}
.course-details-content-area .block-card blockquote {
  margin-bottom: 0;
}
.course-details-content-area .block-card blockquote p {
  color: #000;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
  font-family: var(--ff-heading);
}
.course-details-content-area .block-card .block-icon {
  position: absolute;
  top: 20px;
  right: 20px;
}
.course-details-content-area .block-card .block-icon i {
  font-size: 36px;
  color: #E0E0E0;
}

.blog-details-desc {
  margin-top: 40px;
}
.blog-details-desc h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 10px;
}
.blog-details-desc p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}

.blog-details-bg {
  margin-top: 60px;
  margin-bottom: 30px;
}
.blog-details-bg img {
  border-radius: 10px;
}

.user-link-info {
  display: flex;
  align-items: center;
}
.user-link-info .like-icon {
  height: 50px;
  width: 50px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 25px;
  border: 1px solid #E0E0E0;
  text-align: center;
}
.user-link-info .like-icon i {
  line-height: 46px;
  font-size: 24px;
  color: #212121;
}
.user-link-info p {
  margin-bottom: 0;
}

.feedback-card-area {
  border-top: 1px solid #BDBDBD;
  border-bottom: 1px solid #BDBDBD;
  padding-top: 50px;
  padding-bottom: 50px;
  margin-top: 50px;
}
.feedback-card-area .feedback-card-img-content {
  display: flex;
}
.feedback-card-area .feedback-card-img-content .feedback-img {
  flex: 0 0 120px;
  margin-right: 20px;
}
.feedback-card-area .feedback-card-img-content .feedback-img img {
  height: 120px;
  width: 120px;
  border-radius: 50%;
}
.feedback-card-area .feedback-card-img-content .feedback-content-text .feedback-header {
  margin-top: 20px;
}
.feedback-card-area .feedback-card-img-content .feedback-content-text .feedback-header h4 {
  color: #191E24;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.feedback-card-area .feedback-card-img-content .feedback-content-text .feedback-header p {
  color: #808287;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 20px;
}
.feedback-card-area .feedback-card-img-content .feedback-content-text p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 20px;
}
.feedback-card-area .feedback-card-img-content .feedback-content-text .feedback-share-icon {
  padding-left: 0;
}
.feedback-card-area .feedback-card-img-content .feedback-content-text .feedback-share-icon li {
  display: inline-block;
  list-style: none;
}
.feedback-card-area .feedback-card-img-content .feedback-content-text .feedback-share-icon li a {
  color: #BDBEC5;
  margin-right: 5px;
  transition-duration: 400ms;
}
.feedback-card-area .feedback-card-img-content .feedback-content-text .feedback-share-icon li:hover a {
  color: #F8941F;
}

.comment-area {
  border-bottom: 1px solid #BDBDBD;
  padding-top: 50px;
  padding-bottom: 50px;
}
.comment-area .comment-title {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}

.comment-box-area h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-top: 50px;
}
.comment-box-area p {
  margin-bottom: 40px;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}

.auth-btn.post {
  padding: 15px;
  display: inline-block;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  width: 300px;
  margin-top: 70px;
}

.team-area {
  padding-top: 140px;
  overflow: hidden;
}
.team-area.single-page-team {
  padding-bottom: 110px;
}
.team-area.single-page-team .single-team-card {
  margin-bottom: 30px;
}

.team-block {
  position: relative;
}
.team-block .single-team-card {
  padding: 15px;
  position: relative;
  overflow: hidden;
}
.team-block .single-team-card .border-s-1 {
  background-color: #E0E0E0;
  position: absolute;
  height: 1px;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  transition-duration: 500ms;
}
.team-block .single-team-card .border-s-2 {
  background-color: #E0E0E0;
  position: absolute;
  height: 65%;
  width: 1px;
  top: 0;
  left: 0;
  z-index: 999;
  transition-duration: 500ms;
}
.team-block .single-team-card .border-s-3 {
  background-color: #E0E0E0;
  position: absolute;
  height: 64%;
  width: 1px;
  top: 0;
  right: 0;
  z-index: 999;
  transition-duration: 500ms;
}
.team-block .single-team-card .border-s-4 {
  background-color: #E0E0E0;
  position: absolute;
  height: 132%;
  width: 1px;
  bottom: -30%;
  right: 0%;
  z-index: 999;
  transform: rotate(51deg);
  transition-duration: 500ms;
}
.team-block .single-team-card .border-s-5 {
  background-color: #E0E0E0;
  position: absolute;
  height: 132%;
  width: 1px;
  bottom: -31%;
  left: 0%;
  z-index: 999;
  transform: rotate(-51deg);
  transition-duration: 500ms;
}
.team-block .single-team-card:hover .border-s-1 {
  background-color: #808287;
}
.team-block .single-team-card:hover .border-s-2 {
  background-color: #808287;
}
.team-block .single-team-card:hover .border-s-3 {
  background-color: #808287;
}
.team-block .single-team-card:hover .border-s-4 {
  background-color: #808287;
}
.team-block .single-team-card:hover .border-s-5 {
  background-color: #808287;
}
.team-block .single-team-card .team-img::after {
  position: absolute;
  content: "";
  background-color: #fff;
  height: 100%;
  width: 110px;
  bottom: -116px;
  left: -21%;
  transform: rotate(-51deg);
  z-index: 9;
}
.team-block .single-team-card .team-img::before {
  position: absolute;
  content: "";
  background-color: #fff;
  height: 100%;
  width: 110px;
  bottom: -116px;
  right: -21%;
  transform: rotate(51deg);
  z-index: 9;
}
.team-block .single-team-card .team-img .member-info {
  position: absolute;
  bottom: -1%;
  left: 50%;
  width: 100%;
  transform: translateX(-50%);
  background-color: #EF5C72;
  padding-top: 18px;
  padding-bottom: 60px;
  opacity: 0;
  transition-duration: 500ms;
}
.team-block .single-team-card .team-img .member-info h3 {
  color: #fff;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.team-block .single-team-card .team-img .member-info p {
  margin-bottom: 0;
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.team-block .single-team-card .contact-social-icon .share-icon .share--icon {
  position: absolute;
  bottom: 18%;
  left: 9px;
  z-index: 999;
  background-color: #F8941F;
  height: 40px;
  width: 40px;
  line-height: 40px;
  display: inline-block;
  font-size: 19px;
  color: #fff;
  border-radius: 50%;
  text-align: center;
  transition-duration: 500ms;
  cursor: pointer;
}
.team-block .single-team-card .contact-social-icon .conatct-icon- {
  position: absolute;
  bottom: -400px;
  left: 8px;
  transition-duration: 500ms;
  z-index: 1;
}
.team-block .single-team-card .contact-social-icon .conatct-icon- ul {
  padding-left: 0;
  background-color: #fff;
  border-radius: 22.5px;
  padding-top: 15px;
  padding-left: 12px;
  padding-right: 12px;
  padding-bottom: 45px;
}
.team-block .single-team-card .contact-social-icon .conatct-icon- ul li {
  list-style: none;
  margin-bottom: 8px;
}
.team-block .single-team-card .contact-social-icon .conatct-icon- ul li a {
  color: #BDBEC5;
  font-size: 17px;
}
.team-block .single-team-card .contact-social-icon .conatct-icon- ul li a:hover {
  color: #F8941F;
}
.team-block .single-team-card .contact-social-icon .share-icon:hover .conatct-icon- {
  bottom: 22%;
}
.team-block .single-team-card:hover .member-info {
  opacity: 1;
}

.learn-skill-area {
  padding: 130px 0;
  padding-bottom: 40px;
  overflow: hidden;
}
.learn-skill-area .skill-shape {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.learn-skill-area .skill-shape-2 {
  position: absolute;
  right: 50%;
  top: 26%;
  transform: translateX(50%);
  z-index: 1;
}
.learn-skill-area .skill-bg-img {
  position: relative;
  z-index: 99;
  transform: translateY(10%);
}
.learn-skill-area .skill-bg-img img {
  border-radius: 225px 225px 0px 0px;
  background: lightgray -333.854px -4px/180.045% 100.746% no-repeat, #D9D9D9;
}
.learn-skill-area .skill-content-text {
  position: relative;
  z-index: 101010;
}
.learn-skill-area .skill-content-text h6 {
  color: #808287;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.learn-skill-area .skill-content-text h2 {
  color: #fff;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
}
.learn-skill-area .skill-content-text p {
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-top: 20px;
  margin-bottom: 30px;
}
.learn-skill-area .skill-content-text .skill-program-card:first-child {
  margin-right: 90px;
}
.learn-skill-area .skill-content-text .skill-program-card .program-icon {
  margin-right: 19px;
}
.learn-skill-area .skill-content-text .skill-program-card .program-icon span {
  font-size: 50px;
  color: #F8941F;
}
.learn-skill-area .skill-content-text .skill-program-card .program-icon .icon-pink {
  color: #EF5C72;
}
.learn-skill-area .skill-content-text .skill-program-card .skill-program-text h4 {
  color: #fff;
  font-size: 24px;
  margin-bottom: 0;
}

.footer-wrap {
  position: relative;
  overflow: hidden;
}
.footer-wrap .f-shape-1 {
  position: absolute;
  right: 0;
  top: 0;
}
.footer-wrap .f-shape-2 {
  position: absolute;
  left: -30px;
  top: 0;
}
.footer-wrap .f-shape-3 {
  position: absolute;
  left: 0;
  bottom: -50px;
}
.footer-wrap .footer-content {
  padding-top: 80px;
  padding-bottom: 60px;
  position: relative;
  z-index: 99;
}
.footer-wrap .footer-content .footer-desc {
  color: #FFF;
  font-family: var(--ff-heading);
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 25px;
}
.footer-wrap .footer-content .footer-contact-info ul {
  padding-left: 0;
}
.footer-wrap .footer-content .footer-contact-info ul li {
  display: inline-block;
  margin-right: 10px;
}
.footer-wrap .footer-content .footer-contact-info ul li a {
  height: 40px;
  width: 40px;
  color: #fff;
  line-height: 40px;
  display: inline-block;
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  text-align: center;
  transition-duration: 400ms;
}
.footer-wrap .footer-content .footer-contact-info ul li a span {
  line-height: 40px;
}
.footer-wrap .footer-content .footer-contact-info ul li a:hover {
  background-color: #F8941F;
}
.footer-wrap .footer-content .quick-contact-nav {
  position: relative;
}
.footer-wrap .footer-content .quick-contact-nav ul {
  margin-top: 30px;
}
.footer-wrap .footer-content .quick-contact-nav h5 {
  position: relative;
}
.footer-wrap .footer-content .quick-contact-nav h5::after {
  position: absolute;
  content: "";
  left: 0;
  bottom: -10px;
  background-color: #F8941F;
  height: 1px;
  width: 90px;
}
.footer-wrap .footer-content .quick-contact-nav h5::before {
  position: absolute;
  content: "";
  left: 0;
  bottom: -15px;
  background-color: #F8941F;
  height: 1px;
  width: 58px;
}
.footer-wrap .footer-content .quick-contact-nav ul {
  padding-left: 0;
}
.footer-wrap .footer-content .quick-contact-nav ul li {
  list-style: none;
  margin-bottom: 8px;
}
.footer-wrap .footer-content .quick-contact-nav ul li p {
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  display: flex;
  margin-bottom: 0;
}
.footer-wrap .footer-content .quick-contact-nav ul li p span {
  margin-right: 10px;
  line-height: 1.9;
  color: #F8941F;
}
.footer-wrap .footer-content .quick-links-nav h5 {
  color: #fff;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 10px;
  position: relative;
}
.footer-wrap .footer-content .quick-links-nav h5::after {
  position: absolute;
  content: "";
  left: 0;
  bottom: -10px;
  background-color: #F8941F;
  height: 1px;
  width: 90px;
}
.footer-wrap .footer-content .quick-links-nav h5::before {
  position: absolute;
  content: "";
  left: 0;
  bottom: -15px;
  background-color: #F8941F;
  height: 1px;
  width: 58px;
}
.footer-wrap .footer-content .quick-links-nav .f-news-dec {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 30px;
  margin-top: 30px;
}
.footer-wrap .footer-content .quick-links-nav .footer-serach-bar-four .form-control {
  border: 1px solid #303030;
  background-color: transparent;
  border-radius: 0;
  -webkit-text-fill-color: #808287;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  height: 50px;
}
.footer-wrap .footer-content .quick-links-nav .footer-serach-bar-four .search-btn {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #F8941F;
  height: 100%;
  border: none;
  padding: 0 12px;
}
.footer-wrap .footer-content .quick-links-nav .footer-serach-bar-four .search-btn i {
  color: #fff;
}
.footer-wrap .footer-content .quick-links-nav ul {
  padding-left: 0;
  margin-top: 30px;
}
.footer-wrap .footer-content .quick-links-nav ul li {
  list-style: none;
  margin-bottom: 8px;
}
.footer-wrap .footer-content .quick-links-nav ul li:last-child {
  margin-bottom: 0;
}
.footer-wrap .footer-content .quick-links-nav ul li a {
  color: #808287;
  font-family: var(--ff-body);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  transition-duration: 500ms;
}
.footer-wrap .footer-content .quick-links-nav ul li a:hover {
  color: #F8941F;
}
.footer-wrap .f-gallery-area {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 15px;
  margin-top: 30px;
}
.footer-wrap .f-gallery-img img {
  width: 100%;
  border-radius: 5px;
}
.footer-wrap .copy-right-area .copy-right-text {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 25px 0;
}
.footer-wrap .copy-right-area .copy-right-text p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}
.footer-wrap .copy-right-area .copy-right-text p a {
  color: #F8941F;
  text-decoration: underline;
}

.copy-right-area {
  position: relative;
  z-index: 99;
}

.client-area {
  position: relative;
  z-index: 1;
  padding-top: 140px;
  padding-bottom: 140px;
}
.client-area .swiper {
  padding: 60px 30px;
  padding-right: 0;
}
.client-area .client-active-1-button-prev {
  background-color: #191E24;
  height: 60px;
  width: 60px;
  line-height: 60px;
  color: #fff;
  border-radius: 50%;
  border: none;
  font-size: 20px;
  margin-right: 15px;
  transition-duration: 400ms;
}
.client-area .client-active-1-button-prev:hover {
  background-color: #F8941F;
}
.client-area .client-active-1-button-next {
  background-color: #191E24;
  height: 60px;
  width: 60px;
  line-height: 60px;
  color: #fff;
  border-radius: 50%;
  border: none;
  font-size: 20px;
  transition-duration: 400ms;
}
.client-area .client-active-1-button-next:hover {
  background-color: #F8941F;
}
.client-area .client-map-img {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.client-area .clinet-heading-review-area {
  display: flex;
  align-items: center;
  width: 100%;
  padding-left: 10%;
}
.client-area .clinet-heading-review-area .clinet-heading {
  flex: 0 0 40%;
  width: 40%;
  padding-right: 70px;
  text-align: left !important;
  margin-bottom: 0;
}
.client-area .clinet-heading-review-area .clinet-heading p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 30px;
}
.client-area .clinet-heading-review-area .clinet-slider-area {
  flex: 0 0 60%;
  width: 60%;
}
.client-area .clinet-heading-review-area .clinet-slider-area .client-info-meta p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-top: 60px;
}
.client-area .clinet-heading-review-area .clinet-slider-area .client-info-meta .clinet-img {
  margin-right: 30px;
}
.client-area .clinet-heading-review-area .clinet-slider-area .client-info-meta .clinet-img img {
  border-radius: 50%;
  background: lightgray 50%/cover no-repeat;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.25);
  height: 110px;
  width: 110px;
}
.client-area .clinet-heading-review-area .clinet-slider-area .client-info-meta .clinet-rating ul {
  padding-left: 0;
}
.client-area .clinet-heading-review-area .clinet-slider-area .client-info-meta .clinet-rating ul li {
  list-style: none;
  display: inline-block;
}
.client-area .clinet-heading-review-area .clinet-slider-area .client-info-meta .clinet-rating ul li span {
  color: #F8941F;
}

.client-info-meta {
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 4px 20px 0px rgba(181, 162, 162, 0.1);
  padding: 45px 35px;
  position: relative;
}

.client-footer {
  margin-top: 20px;
}
.client-footer .user-icon {
  height: 55px;
  flex: 0 0 55px;
  width: 55px;
  line-height: 55px;
  background-color: #808287;
  border-radius: 50%;
  text-align: center;
  line-height: 55px;
  font-size: 35px;
  color: #fff;
  margin-right: 15px;
}
.client-footer .client-info h4 {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.client-footer .client-info p {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 0 !important;
  margin-bottom: 0;
}
.client-footer .footer-icon-c span {
  color: #E0E0E0;
  font-size: 50px;
  transition-duration: 400ms;
}

.swiper-slide-active .client-footer .user-icon {
  background-color: #6956F9;
}

.client-img-rating.d-flex.align-items-end {
  position: absolute;
  top: -45px;
  left: 44px;
}

.client-area-home-2 {
  padding-bottom: 120px;
  background-color: #F2F8F8;
}
.client-area-home-2 .client-2-shape-1 {
  position: absolute;
  right: 0;
  bottom: 0;
}
.client-area-home-2 .client-image-home-2 img {
  width: 100%;
  padding: 20px;
}
.client-area-home-2 .client-image-home-2 .c-2-border-1 {
  border-right: 1px dashed #F8941F;
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  z-index: 10;
}
.client-area-home-2 .client-image-home-2 .c-2-border-2 {
  border-top: 1px dashed #F8941F;
  position: absolute;
  top: 0;
  right: 0;
  width: 77%;
  z-index: 10;
}
.client-area-home-2 .client-image-home-2 .c-2-border-3 {
  border-top: 1px dashed #F8941F;
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: 10;
}
.client-area-home-2 .client-image-home-2 .c-2-border-4 {
  border-left: 1px dashed #F8941F;
  position: absolute;
  bottom: 0;
  left: 0;
  height: 76%;
  z-index: 10;
}
.client-area-home-2 .client-image-home-2 .c-2-border-5 {
  border-top: 1px dashed #F8941F;
  position: absolute;
  top: 12%;
  left: -5%;
  width: 33%;
  transform: rotate(133deg);
  z-index: 10;
}
.client-area-home-2 button.client-active-1-button-prev,
.client-area-home-2 button.client-active-1-button-next {
  background-color: #F2F2F2;
  height: 40px;
  width: 40px;
  line-height: 40px;
  color: #808287;
  border: none;
  border-radius: 50%;
  transition-duration: 400ms;
}
.client-area-home-2 button.client-active-1-button-prev:hover,
.client-area-home-2 button.client-active-1-button-next:hover {
  background-color: #808287;
  color: #fff;
}
.client-area-home-2 .client-curbe-shape {
  position: absolute;
  left: 0;
  top: 0;
}
.client-area-home-2 .client-active-2 {
  border-radius: 20px;
  border: 1px dashed #F8941F;
  background: #fff;
  padding: 30px 70px;
  padding-bottom: 60px;
}
.client-area-home-2 .client-active-2 .footer-icon-home-2 {
  position: absolute;
  right: 0;
  top: 0;
}
.client-area-home-2 .client-active-2 .footer-icon-home-2 span {
  color: #E0E0E0;
  font-size: 60px;
  transform: rotate(-90deg);
}
.client-area-home-2 .client-active-2 .client-info-meta-home-2 {
  margin-top: 45px;
}
.client-area-home-2 .client-active-2 .client-info-meta-home-2 p {
  color: #808287;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}
.client-area-home-2 .clinet-rating ul {
  padding: 0;
  margin-bottom: 20px;
}
.client-area-home-2 .clinet-rating ul li {
  list-style: none;
  display: inline-block;
  margin-right: 5px;
}
.client-area-home-2 .clinet-rating ul li span {
  color: #F8941F;
}
.client-area-home-2 .client-image-home-2 {
  position: relative;
  z-index: 1;
}
.client-area-home-2 .client-image-home-2::after {
  position: absolute;
  content: "";
  background-color: #F2F8F8;
  height: 100px;
  width: 250px;
  top: 0;
  left: -29%;
  transform: rotate(-47deg);
}

.client-area-4 {
  padding-top: 120px;
  padding-bottom: 120px;
  background: #F3F9F9;
  overflow: hidden;
}
.client-area-4 .client-4-shape-1 {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 1;
}
.client-area-4 .client-info-meta-home-4 p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}
.client-area-4 .client-info-meta-home-4 .client-info p {
  font-size: 14px;
}
.client-area-4 .client-footer .user-icon {
  background-color: #6956F9;
}
.client-area-4 .clinet-single-slider {
  border-radius: 20px;
  border: 1px dashed #F8941F;
  background: #fff;
  padding: 50px 40px;
  padding-top: 80px;
  position: relative;
}
.client-area-4 .clinet-single-slider .footer-icon-home-4 {
  position: absolute;
  right: 20px;
  top: 0;
}
.client-area-4 .clinet-single-slider .footer-icon-home-4 span {
  color: #E0E0E0;
  font-size: 110px;
  transform: rotate(-90deg);
}
.client-area-4 .clinet-single-slider .clinet-rating ul {
  padding: 0;
  margin-bottom: 20px;
}
.client-area-4 .clinet-single-slider .clinet-rating ul li {
  list-style: none;
  display: inline-block;
  margin-right: 5px;
}
.client-area-4 .clinet-single-slider .clinet-rating ul li span {
  color: #F8941F;
}

.client-area-3 {
  padding-top: 140px;
  padding-bottom: 140px;
  background-color: #fff;
}
.client-area-3 .client-shape-3 {
  position: absolute;
  bottom: 0;
  right: 3%;
}
.client-area-3 .client-nav-wrapper .client-nav {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px 0;
}
.client-area-3 .client-nav {
  padding-left: 0;
}
.client-area-3 .client-nav li {
  list-style: none;
  margin: 0;
  border: 10px solid #fff;
  box-shadow: 0px 4px 30px 0px rgba(25, 30, 36, 0.1);
  cursor: pointer;
}
.client-area-3 .client-nav li img {
  border-radius: 0;
  width: 100%;
  opacity: 0.6;
  transition-duration: 500ms;
}
.client-area-3 .client-nav li.tns-nav-active img {
  opacity: 1;
}
.client-area-3 .client-nav .client-slider-icon {
  border-radius: 50%;
  box-shadow: 0px 4px 20px rgba(146, 113, 113, 0.25) !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
.client-area-3 .client-nav .client-slider-icon i {
  color: #6956F9;
  font-size: 40px;
}
.client-area-3 .single-client-3 {
  padding-left: 100px;
}
.client-area-3 .single-client-3 .footer-icon-home-3 {
  position: absolute;
  top: -26%;
  right: 0;
}
.client-area-3 .single-client-3 .footer-icon-home-3 i {
  color: #E0E0E0;
  font-size: 80px;
  transform: rotate(-90deg);
}
.client-area-3 .single-client-3 .client-title-area ul {
  padding-left: 0;
  margin-bottom: 20px;
}
.client-area-3 .single-client-3 .client-title-area ul li {
  list-style: none;
  display: inline-block;
}
.client-area-3 .single-client-3 .client-title-area ul li i {
  color: #F8941F;
}
.client-area-3 .single-client-3 .client-title-area p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 30px;
}
.client-area-3 .single-client-3 .client-title-area .user-icon {
  height: 55px;
  width: 55px;
  border-radius: 50%;
  background-color: #6956F9;
  text-align: center;
  line-height: 55px;
  margin-right: 15px;
}
.client-area-3 .single-client-3 .client-title-area .user-icon span {
  color: #fff;
  line-height: 55px;
  font-size: 32px;
}
.client-area-3 .single-client-3 .client-title-area .client-info h4 {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.client-area-3 .single-client-3 .client-title-area .client-info p {
  margin-bottom: 0;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.care-program-area {
  padding-bottom: 140px;
  padding-top: 140px;
}
.care-program-area .single-care-card {
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 4px 40px 0px rgba(237, 213, 213, 0.2);
  padding: 50px;
  padding-top: 80px;
}
.care-program-area .single-care-card .care-shape {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.care-program-area .single-care-card .care-card-img-content .care-img {
  position: relative;
  z-index: 9;
}
.care-program-area .single-care-card .care-card-img-content .care-img img {
  border-radius: 10px;
}
.care-program-area .single-care-card .care-card-img-content .care-img::after {
  position: absolute;
  content: "";
  background-color: #fff;
  left: 19%;
  bottom: -67%;
  height: 116%;
  width: 90px;
  transform: rotate(-58deg);
}
.care-program-area .single-care-card .care-card-img-content .care-img::before {
  position: absolute;
  content: "";
  background-color: #fff;
  right: 19%;
  bottom: -67%;
  height: 116%;
  width: 90px;
  transform: rotate(58deg);
}
.care-program-area .single-care-card .care-card-img-content .care-content-text {
  position: relative;
  z-index: 10;
}
.care-program-area .single-care-card .care-card-img-content .care-content-text h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-top: 25px;
}
.care-program-area .single-care-card .care-card-img-content .care-content-text p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
}
.care-program-area .single-care-card .care-card-img-content .care-content-text .care-count-info {
  margin-top: 25px;
  opacity: 0;
  transition-duration: 500ms;
}
.care-program-area .single-care-card .care-card-img-content .care-content-text .care-count-info p {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.care-program-area .single-care-card .care-card-img-content .care-content-text .care-count-info p span {
  color: #F8941F;
}
.care-program-area .single-care-card:hover .care-content-text .care-count-info {
  opacity: 1;
}

.upcoming-event-area {
  padding-top: 140px;
  padding-bottom: 140px;
}
.upcoming-event-area .up-shape-1 {
  position: absolute;
  top: 25%;
  left: 10%;
  z-index: 1;
  animation: swing 7s infinite;
}
.upcoming-event-area .up-shape-2 {
  position: absolute;
  top: 15%;
  right: 10%;
  z-index: 1;
}
.upcoming-event-area .up-shape-3 {
  position: absolute;
  bottom: 20%;
  right: 5%;
  z-index: 1;
}
.upcoming-event-area .event-image img {
  border-radius: 10px;
  transform: rotate(10.917deg);
}
.upcoming-event-area .single-event-card {
  border-bottom: 1px dashed #E0E0E0;
  padding-bottom: 30px;
  margin-bottom: 40px;
  transition-duration: 700ms;
}
.upcoming-event-area .single-event-card:last-child {
  margin-bottom: 0;
}
.upcoming-event-area .single-event-card .event-price {
  background-color: #f2f2f2;
  height: 100px;
  width: 100px;
  flex: 0 0 100px;
  border-radius: 50%;
  margin-right: 50px;
  transition-duration: 500ms;
}
.upcoming-event-area .single-event-card .event-price h5 {
  text-align: center;
  font-size: 24px;
  line-height: 100px;
  font-weight: 600;
  transition-duration: 400ms;
}
.upcoming-event-area .single-event-card .event-card-content p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 5px;
}
.upcoming-event-area .single-event-card .event-card-content h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.upcoming-event-area .single-event-card:hover {
  border-color: #F8941F;
  margin-left: 100px;
}
.upcoming-event-area .single-event-card:hover .event-price {
  background-color: #F8941F;
}
.upcoming-event-area .single-event-card:hover .event-price h5 {
  color: #fff;
}

.event-list-inner-page-area {
  padding-top: 140px;
  padding-bottom: 140px;
}
.event-list-inner-page-area.sidebar-event-page {
  padding-bottom: 110px;
}

.cate-single-card {
  background: #fff;
  box-shadow: 0px 4px 40px 0px rgba(229, 205, 205, 0.2);
  padding: 15px;
  margin-bottom: 40px;
}
.cate-single-card .cate-content-card {
  border: 1px dashed #E0E0E0;
  padding: 20px;
}
.cate-single-card .cate-content-card h3 {
  border-bottom: 1px dashed #E0E0E0;
  padding-bottom: 10px;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.cate-single-card .cate-content-card .cate-list {
  padding-left: 0;
  margin-top: 20px;
}
.cate-single-card .cate-content-card .cate-list li {
  display: inline-block;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  font-family: var(--font-body);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.cate-single-card .cate-content-card .cate-list li a {
  color: #191E24;
  transition-duration: 500ms;
}
.cate-single-card .cate-content-card .cate-list li span {
  color: #191E24;
}
.cate-single-card .cate-content-card .cate-list li span i {
  margin-right: 2px;
}
.cate-single-card .cate-content-card .cate-list li:last-child {
  margin-bottom: 0;
}
.cate-single-card .cate-content-card .cate-list li:hover a {
  color: #808287;
  transform: translateX(10px);
}
.cate-single-card .cate-content-card .cate-list li:hover span {
  color: #808287;
}

.event-card-single-inner {
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 4px 40px 0px rgba(237, 213, 213, 0.2);
  padding: 30px;
  margin-bottom: 30px;
}
.event-card-single-inner .event-img-inner img {
  border-radius: 10px;
}
.event-card-single-inner .add-time-info {
  margin-top: 30px;
}
.event-card-single-inner .add-time-info p {
  color: #808287;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-right: 20px;
}
.event-card-single-inner .add-time-info p i {
  color: #EF5C72;
  margin-right: 3px;
  font-size: 16px;
}
.event-card-single-inner h3 a {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 25px;
  color: #191E24;
  display: block;
  transition-duration: 500ms;
}
.event-card-single-inner h3:hover a {
  color: #EF5C72;
}
.event-card-single-inner .event-btn {
  color: #EF5C72;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  font-family: var(--font-body);
  border-radius: 75px;
  border: 1px solid #E0E0E0;
  padding: 10px 20px;
  display: inline-block;
  transition-duration: 500ms;
}
.event-card-single-inner .event-btn:hover {
  background-color: #EF5C72;
  border-color: #EF5C72;
  color: #fff;
}

.pagination-area .pagination {
  padding-left: 0;
}
.pagination-area .pagination .page-item:first-child .page-link {
  border-radius: 3px;
}
.pagination-area .pagination .page-item:last-child .page-link {
  border-radius: 3px;
}
.pagination-area .pagination li {
  margin: 0 7px;
}
.pagination-area .pagination li a {
  padding: 10px 20px;
  color: #191E24;
  font-size: 20px;
  font-weight: 400;
  border: 1px solid #E0E0E0;
  border-radius: 3px;
  transition-duration: 500ms;
}
.pagination-area .pagination li a i {
  font-size: 40px;
  line-height: 7px;
  position: relative;
  top: 10px;
}
.pagination-area .pagination li a:hover {
  background-color: #6956F9;
  color: #fff;
}
.pagination-area .pagination li .last-pagi {
  padding: 10px 6px;
}

.news-cate-area {
  margin-top: 15px;
  margin-bottom: 15px;
}
.news-cate-area:last-child {
  margin-bottom: 0;
}
.news-cate-area .news-img {
  flex: 0 0 75px;
  width: 75px;
  margin-right: 12px;
}
.news-cate-area .news-img img {
  height: 65px;
  width: 100%;
  border-radius: 5px;
}
.news-cate-area .news-content-text h6 a {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: #191E24;
}
.news-cate-area .news-content-text h6:hover a {
  color: #6956F9;
}
.news-cate-area .news-content-text p {
  margin-bottom: 0;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.news-cate-area .news-content-text p i {
  color: #EF5C72;
  margin-right: 5px;
}

.tags-list {
  padding-left: 0;
}
.tags-list li {
  list-style: none;
  display: inline-block;
  margin-right: 10px;
  margin-top: 10px;
}
.tags-list li a {
  padding: 5px 10px;
  display: inline-block;
  color: #191E24;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  border-radius: 3px;
  border: 1px solid #E0E0E0;
  transition-duration: 400ms;
}
.tags-list li a:hover {
  background-color: #6956F9;
  color: #fff;
  border-color: #6956F9;
}

.event-card-single-inner.sidebar-event {
  padding: 30px 25px;
}
.event-card-single-inner.sidebar-event .add-time-info p {
  font-size: 12px;
  margin-right: 10px;
}
.event-card-single-inner.sidebar-event .add-time-info p i {
  margin-right: 2px;
  font-size: 13px;
}
.event-card-single-inner.sidebar-event h3 a {
  font-size: 20px;
}

.event-details-tab-content h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 30px;
}
.event-details-tab-content .event-details-list {
  padding-left: 0;
}
.event-details-tab-content .event-details-list li {
  list-style: none;
  color: #808287;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 20px;
  display: flex;
}
.event-details-tab-content .event-details-list li .event-icon-single {
  margin-right: 20px;
}
.event-details-tab-content .event-details-list li .event-icon-single i {
  color: #F8941F;
}

.event-desc-area .desc-map-area {
  margin-bottom: 70px;
}
.event-desc-area .desc-map-area iframe {
  height: 400px;
  width: 100%;
}

.event-desc-area {
  border-top: 1px solid #BDBDBD;
  padding-top: 50px;
  margin-top: 50px;
}
.event-desc-area h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 20px;
}
.event-desc-area p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 40px;
}

.event-member-area h4 {
  margin-bottom: 40px;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.course-share-icon-area {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;
}
.course-share-icon-area p {
  margin-bottom: 0;
  font-family: var(--font-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-right: 10px;
}
.course-share-icon-area .course-share-icon {
  padding-left: 0;
}
.course-share-icon-area .course-share-icon li {
  display: inline-block;
  list-style: none;
  margin: 0 5px;
}
.course-share-icon-area .course-share-icon li a {
  color: #808287;
  font-size: 20px;
  transition-duration: 400ms;
}
.course-share-icon-area .course-share-icon li a i {
  position: relative;
  top: 3px;
}
.course-share-icon-area .course-share-icon li:hover a {
  color: #F8941F;
}

.course-info-list.event li:last-child {
  border-bottom: 1px solid #E0E0E0;
  padding-bottom: 20px !important;
}

.faq-area {
  padding: 140px 0;
  padding-bottom: 60px;
  position: relative;
}
.faq-area .faq-shape-1 {
  position: absolute;
  left: 0;
  top: 50%;
  z-index: -1;
  transform: translateY(-50%);
}
.faq-area .faq-shape-2 {
  position: absolute;
  top: 10%;
  right: 10%;
  animation: spin 40s linear infinite;
}
.faq-area .faq-content-area.single-page-faq h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 30px;
}
.faq-area .faq-content-area .accordion-item {
  margin-bottom: 30px;
  border: none;
  border-radius: 20px;
  box-shadow: 0px 4px 40px 0px rgba(221, 203, 203, 0.25);
}
.faq-area .faq-content-area .accordion-item .accordion-body {
  border-left: 1px dashed #6956F9;
  border-right: 1px dashed #6956F9;
  border-bottom: 1px dashed #6956F9;
  border-radius: 0 0 20px 20px;
  padding-top: 0;
}
.faq-area .faq-content-area .accordion-item .accordion-body p {
  color: #808287;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
  border-top: 1px dashed #6956F9;
  padding-top: 20px;
}
.faq-area .faq-content-area .accordion-button {
  border-radius: 20px;
  border: 1px dashed #6956F9;
  background: #fff;
  box-shadow: 0px 4px 40px 0px rgba(221, 203, 203, 0.25);
  color: #191E24;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.faq-area .faq-content-area .accordion-button.collapsed {
  border-bottom: 1px dashed #6956F9 !important;
  border-radius: 20px !important;
}
.faq-area .faq-content-area .accordion-item .accordion-button {
  border-bottom: none;
  border-radius: 10px 10px 0 0;
  box-shadow: none;
}

.contact-us-area {
  padding-bottom: 140px;
}
.contact-us-area.page-2 {
  padding-top: 140px;
  padding-bottom: 200px;
}
.contact-us-area .contact-img {
  border: 1px dashed #F8941F;
  padding: 20px;
  border-radius: 20px;
}
.contact-us-area .contact-img img {
  border-radius: 20px;
}
.contact-us-area .conatct-content-area {
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0px 4px 40px 0px rgba(221, 203, 203, 0.25);
}

.contact-map iframe {
  height: 600px;
  width: 100%;
  margin-bottom: -8px;
}
.contact-map .conatct-info-meta {
  position: absolute;
  top: -90px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 15px;
}
.contact-map .conatct-info-meta .contact-single-info-card {
  background: #fff;
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.1);
  padding: 30px 60px;
}
.contact-map .conatct-info-meta .contact-single-info-card .contact-icon {
  margin-bottom: 20px;
}
.contact-map .conatct-info-meta .contact-single-info-card .contact-icon i {
  color: #F8941F;
  font-size: 30px;
}
.contact-map .conatct-info-meta .contact-single-info-card h4 {
  color: #6956F9;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 10px;
}
.contact-map .conatct-info-meta .contact-single-info-card p {
  color: #5D666F;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
}

.certificate-area {
  padding-top: 140px;
}
.certificate-area .gra-shape-1 {
  position: absolute;
  top: 10%;
  left: 16%;
}
.certificate-area .gra-shape-3 {
  position: absolute;
  bottom: 3%;
  left: 31%;
  z-index: 1;
}
.certificate-area .gra-shape-2 {
  position: absolute;
  top: 4%;
  left: 15%;
  z-index: 1;
}
.certificate-area .gra-shape-4 {
  position: absolute;
  top: 10%;
  right: 5%;
  z-index: 9;
  animation: swing 7s infinite;
}
.certificate-area .gra-cert-image-area .certi-img {
  display: flex;
  justify-content: end;
}
.certificate-area .gra-cert-image-area .certi-img .gra-img-1 {
  border-radius: 30px;
  border: 9px solid #808287;
  padding: 15px;
  width: 80%;
  display: flex;
}
.certificate-area .gra-cert-image-area .certi-img .gra-img-1 img {
  border-radius: 20px;
  width: 100%;
}
.certificate-area .gra-cert-image-area .certi-img-2 {
  position: absolute;
  top: 60%;
  left: 0;
}
.certificate-area .gra-cert-image-area .certi-img-2 .gra-img-2 {
  border-radius: 30px;
  border: 9px solid #F8941F;
  padding: 15px;
  width: 80%;
  background-color: #fff;
}
.certificate-area .gra-cert-image-area .certi-img-2 .gra-img-2 img {
  border-radius: 20px;
  width: 100%;
}
.certificate-area .certi-title-area {
  padding-left: 8%;
}
.certificate-area .certi-title-area .certi-sub-title {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.certificate-area .certi-title-area .certi-sub-title span {
  background-color: #F8941F;
  height: 50px;
  width: 50px;
  display: inline-block;
  line-height: 50px;
  border-radius: 50px;
  font-size: 25px;
  text-align: center;
  margin-right: 15px;
  color: #fff;
}
.certificate-area .certi-title-area h2 {
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
}
.certificate-area .certi-single-card {
  margin-top: 40px;
}
.certificate-area .certi-single-card .certi-border {
  border-radius: 50%;
  border: 1px dashed;
  padding: 10px;
  margin-right: 20px;
}
.certificate-area .certi-single-card .certi-border .certi-icon {
  flex: 0 0 80px;
  height: 80px;
  width: 80px;
  line-height: 80px;
  text-align: center;
  border-radius: 50%;
}
.certificate-area .certi-single-card .certi-border .certi-icon img {
  line-height: 80px;
}
.certificate-area .certi-single-card .certi-card-info h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.certificate-area .certi-single-card .certi-card-info p {
  margin-bottom: 0;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}

/*----------------------------------------
   MFP CUSTOMIZE
-----------------------------------------*/
.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 1280px;
}

@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 1000px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 850px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 820px;
  }
}
.mfp-close {
  transform: rotate(0deg);
}

.mfp-close:hover {
  color: #fff;
}

.mfp-close::after {
  position: absolute;
  content: "\f00d";
  height: 100%;
  width: 100%;
  font-family: "Font Awesome 6 Pro";
  font-size: 31px;
  font-weight: 200;
  right: -20px;
  margin-top: -25px;
}

@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-close::after {
    right: 15px;
    margin-top: -30px;
  }
}
.play-btn {
  position: absolute;
  z-index: 1010;
  height: 150px;
  width: 150px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0px 4px 30px rgba(212, 197, 197, 0.25);
  right: 12%;
  bottom: -20%;
  z-index: 1;
}
.play-btn::after {
  position: absolute;
  content: "";
  height: 135px;
  width: 135px;
  border: 1px dashed #808287;
  border-radius: 50%;
  top: 7.5px;
  left: 7.5px;
  transition: all 0.1s 0s ease-out;
  animation: spin 60s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.play-btn .video_player_btn {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: all 500ms ease;
  line-height: 150px;
  z-index: 10;
}
.play-btn .video_player_btn a {
  font-size: 40px;
  color: #191E24;
  transition-duration: 400ms;
}
.play-btn .video_player_btn a:hover {
  color: #F8941F;
}

.portfolio-area {
  padding-top: 280px;
  padding-bottom: 140px;
  background-color: #F3F9F9;
  position: relative;
}
.portfolio-area.single-page-gallery {
  padding-top: 140px;
  padding-bottom: 110px;
}
.portfolio-area.single-page-gallery .portfolio-card .portfolio-image {
  border-radius: 20px;
  border: 1px dashed #F8941F;
  padding: 10px;
  transition-duration: 500ms;
}
.portfolio-area.single-page-gallery .portfolio-card .portfolio-image img {
  border-radius: 20px;
}
.portfolio-area.single-page-gallery .portfolio-card .portfolio-image:hover {
  border: 5px solid #F8941F;
}
.portfolio-area .gallery-curbe-shape {
  position: absolute;
  bottom: 0;
  left: 0;
}
.portfolio-area .g-shape-1 {
  position: absolute;
  top: 12%;
  left: 4%;
}
.portfolio-area .g-shape-2 {
  position: absolute;
  bottom: 0;
  right: 20%;
  animation: spin 40s linear infinite;
}
.portfolio-area .g-shape-3 {
  position: absolute;
  top: 42%;
  left: 43%;
}

.vbox-close {
  top: 2%;
  right: 2%;
  color: #fff;
}

.portfolio-card {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.portfolio-card .portfolio-image {
  position: relative;
  border-radius: 20px;
  min-height: 5rem;
}

.portfolio-card .portfolio-image img {
  transition: all 300ms ease;
  border-radius: 20px;
}

.portfolio-card .portfolio-image .overlay-content {
  transition-duration: 400ms;
  border-radius: 20px;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  padding: 1.5rem;
}

.portfolio-card .portfolio-image .overlay-content a {
  display: block;
  color: #ffffff;
  font-size: 20px;
  background-color: #191E24;
  height: 86px;
  width: 86px;
  line-height: 86px;
  border-radius: 50%;
  position: relative;
  z-index: 9;
}
.portfolio-card .portfolio-image .overlay-content a::after {
  position: absolute;
  content: "";
  top: 5px;
  left: 5px;
  border: 1px dashed #fff;
  height: 76px;
  width: 76px;
  border-radius: 50%;
  z-index: 1;
}
.portfolio-card .portfolio-image .overlay-content a:hover {
  color: #F8941F;
}

.portfolio-card .description {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background-color: #ffffff;
  border-radius: 0 0 0.75rem 0.75rem;
}

.portfolio-card .description a {
  display: block;
  font-size: 1rem;
  color: #1f0757;
}

.portfolio-card .description a:hover,
.portfolio-card .description a:focus {
  color: #003fe0;
}

.portfolio-card .description p {
  font-size: 14px;
  font-weight: 600;
  padding: 0.25rem 0.625rem;
  border: 2px solid #dee1e6;
  line-height: 1;
  border-radius: 0.25rem;
}

.portfolio-card:hover .portfolio-image img,
.portfolio-card:focus .portfolio-image img {
  transform: scale(1.1);
}

.portfolio-card:hover .overlay-content,
.portfolio-card:focus .overlay-content {
  opacity: 1;
  visibility: visible;
}

@media only screen and (min-width: 992px) {
  .gallery-area.gallery-2 .col-12:nth-child(even) > div {
    margin-top: 3rem;
  }
}
.gallery-area.gallery-2 .portfolio-card .description {
  padding-left: 2rem;
  padding-right: 2rem;
  text-align: left;
  background-color: rgba(255, 193, 7, 0.1);
}

.gallery-area.gallery-2 .portfolio-card .description .links {
  display: block;
  line-height: 1;
  font-size: 2rem;
  color: #1f0757;
  transform: translateX(0rem);
}

.gallery-area.gallery-2 .portfolio-card:hover .description .links,
.gallery-area.gallery-2 .portfolio-card:focus .description .links {
  transform: translateX(0.5rem);
  visibility: visible;
}

.border--pri-1 {
  border: 1px dashed #F8941F;
  border-radius: 20px;
  padding: 5px;
  display: inline-block;
}

.border--dark-1 {
  border: 1px dashed #808287;
  border-radius: 20px;
  padding: 5px;
  display: inline-block;
}

.border--navy-1 {
  border: 1px dashed #6956F9;
  border-radius: 20px;
  padding: 5px;
  display: inline-block;
}

.border--pri-2 {
  border: 5px solid #F8941F;
  border-radius: 20px;
  padding: 5px;
  display: inline-block;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.gallery-area-3 .gallery-img-area-3 .gallery-title-3 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.8);
  display: inline-block;
  padding: 10px 15px;
  opacity: 0;
  transition-duration: 400ms;
  width: 200px;
  text-align: center;
}
.gallery-area-3 .gallery-img-area-3 .gallery-title-3 h4 {
  color: #808287;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.gallery-area-3 .gallery-img-area-3 .gallery-title-3 h4 a {
  color: #6956F9;
  font-weight: 600;
}
.gallery-area-3 .swiper-slide.swiper-slide-active .gallery-title-3 {
  opacity: 1;
}

.featured-course-4-area {
  padding-bottom: 110px;
}
.featured-course-4-area.single-page {
  padding-top: 140px;
}
.featured-course-4-area .course-card-4-area {
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 4px 40px 0px rgba(237, 213, 213, 0.2);
  padding: 30px;
  padding-top: 50px;
  position: relative;
  margin-bottom: 30px;
}
.featured-course-4-area .course-card-4-area .course-content-rating {
  margin-top: 30px;
  margin-bottom: 20px;
}
.featured-course-4-area .course-card-4-area .course-content-rating p {
  margin-bottom: 0;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.featured-course-4-area .course-card-4-area .course-content-rating p span {
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}
.featured-course-4-area .course-card-4-area .course-content-rating p i {
  color: #F8941F;
  font-size: 15px;
}
.featured-course-4-area .course-card-4-area .course-info-meta-4 p {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-right: 20px;
}
.featured-course-4-area .course-card-4-area .course-info-meta-4 p:last-child {
  margin-right: 0;
}
.featured-course-4-area .course-card-4-area .course-info-meta-4 p i {
  margin-right: 10px;
}
.featured-course-4-area .course-card-4-area h2 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.featured-course-4-area .course-card-4-area h2 a {
  color: #191E24;
  transition-duration: 400ms;
}
.featured-course-4-area .course-card-4-area h2:hover a {
  color: #F8941F;
}
.featured-course-4-area .course-card-4-area .course-desc-4 {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 30px;
}
.featured-course-4-area .course-card-4-area .auth-info-4 {
  margin-bottom: 30px;
}
.featured-course-4-area .course-card-4-area .auth-info-4 .auth-img {
  margin-right: 15px;
}
.featured-course-4-area .course-card-4-area .auth-info-4 .auth-img img {
  border-radius: 50%;
}
.featured-course-4-area .course-card-4-area .auth-info-4 p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
}
.featured-course-4-area .course-card-4-area .auth-info-4 p a {
  color: var(--Black, #191E24);
  font-family: var(--ff-body);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 29px;
  margin-left: 8px;
}
.featured-course-4-area .course-card-4-area .course-footer-4 {
  border-top: 1px solid #E0E0E0;
  padding-top: 25px;
}
.featured-course-4-area .course-card-4-area .course-footer-4 .course-price p {
  color: #EF5C72;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
  font-family: var(--ff-heading);
}
.featured-course-4-area .course-card-4-area .course-footer-4 .course-price p span {
  color: #808287;
  font-family: var(--ff-heading);
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-decoration: line-through;
  margin-left: 5px;
}
.featured-course-4-area .course-card-4-area .course-footer-4 .course-buy p {
  margin-bottom: 0;
}
.featured-course-4-area .course-card-4-area .course-footer-4 .course-buy p a {
  color: #191E24;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  transition-duration: 400ms;
}
.featured-course-4-area .course-card-4-area .course-footer-4 .course-buy p a:hover {
  color: #F8941F;
}
.featured-course-4-area .course-card-4-area .care-shape-4 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  transition-duration: 500ms;
}
.featured-course-4-area .course-card-4-area .course-img-4 {
  height: 260px;
  border-radius: 10px;
  position: relative;
  z-index: 1;
}
.featured-course-4-area .course-card-4-area .course-img-4 .course-sub-heading-4 {
  position: absolute;
  top: 28%;
  left: 8%;
  z-index: 1010;
}
.featured-course-4-area .course-card-4-area .course-img-4 .course-sub-heading-4 h6 {
  color: #fff;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.featured-course-4-area .course-card-4-area .course-img-4 .course-sub-heading-4 span {
  background-color: #F8941F;
  color: #fff;
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 3px 10px;
  display: inline-block;
  border-radius: 3px;
}
.featured-course-4-area .course-card-4-area .course-img-4 .course-sub-title {
  position: absolute;
  z-index: 101010;
  top: 12%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition-duration: 800ms;
}
.featured-course-4-area .course-card-4-area .course-img-4 .course-sub-title h6 {
  color: #fff;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.featured-course-4-area .course-card-4-area .course-img-4 .course-title-img img {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.featured-course-4-area .course-card-4-area .course-img-4 .course-title-img img.four {
  right: 0;
}
.featured-course-4-area .course-card-4-area .course-img-4 .course-title-img-four img {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 10;
}
.featured-course-4-area .course-card-4-area .course-img-4 .course-shape-4 {
  transition-duration: 900ms;
}
.featured-course-4-area .course-card-4-area .course-img-4 .course-shape-4 img {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.featured-course-4-area .course-card-4-area .course-offer-4 {
  position: absolute;
  bottom: 15px;
  right: 15px;
  z-index: 1010;
}
.featured-course-4-area .course-card-4-area .course-offer-4 .offer-content-4 {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -30%);
}
.featured-course-4-area .course-card-4-area .course-offer-4 .offer-content-4 p {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 1;
  display: block;
  margin-bottom: 0;
}
.featured-course-4-area .course-card-4-area .course-offer-4 .offer-content-4 span {
  color: #fff;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 1;
}
.featured-course-4-area .course-card-4-area:hover .course-sub-title {
  opacity: 1;
}

.fav-course-area-3 {
  padding: 140px 0;
}
.fav-course-area-3 .course-tab-heading {
  margin-bottom: 50px;
}
.fav-course-area-3 .course-tab-heading .nav-link {
  border-radius: 50px;
  background: #fff;
  box-shadow: 0 0 32px 9px rgba(0, 0, 0, 0.04);
  margin: 0 8px;
  border-bottom: 3px solid transparent;
  transition-duration: 400ms;
}
.fav-course-area-3 .course-tab-heading .nav-link.active {
  border-color: #F8941F;
}
.fav-course-area-3 .tab-btn-img-text .tab-btn-img {
  flex: 0 0 30px;
  margin-right: 8px;
}
.fav-course-area-3 .tab-btn-img-text .course-title-4 {
  font-size: 17px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
  color: #191E24;
  font-family: var(--ff-heading);
}
.fav-course-area-3 .tab-btn-img-text .course-nu {
  font-size: 13px;
  font-weight: 400;
  margin-bottom: 0;
  color: #808287;
}
.fav-course-area-3 .course-content-3-area .course-heading h2 {
  color: #191E24;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  border-bottom: 1px solid #E0E0E0;
  padding-bottom: 15px;
  margin-bottom: 30px;
}
.fav-course-area-3 .course-content-3-area .course-heading p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 30px;
}
.fav-course-area-3 .course-content-3-area .course-content-info h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 30px;
}
.fav-course-area-3 .course-content-3-area .course-content-info .course-du-card {
  border-radius: 5px;
  border: 1px solid #E0E0E0;
  padding: 25px;
  margin-bottom: 15px;
}
.fav-course-area-3 .course-content-3-area .course-content-info .course-du-card h3 {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.fav-course-area-3 .course-content-3-area .course-content-info .course-du-card p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
}
.fav-course-area-3 .course-content-3-area .course-content-info .course-du-card:last-child {
  margin-bottom: 0;
}
.fav-course-area-3 .course-image img {
  border-radius: 20px;
}

.course-card-4-area.course-list {
  padding-top: 0;
  padding: 20px;
}
.course-card-4-area.course-list .course-img-4 {
  height: auto;
}
.course-card-4-area.course-list .course-content-info-4.single h2 a {
  color: #191E24;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  transition-duration: 400ms;
}
.course-card-4-area.course-list .course-content-info-4.single h2:hover a {
  color: #F8941F;
}
.course-card-4-area.course-list .course-list-shape-single {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.course-card-4-area.course-list .course-content-info-4.single {
  padding-left: 50px;
  flex: 0 0 65%;
}
.course-card-4-area.course-list .course-content-info-4.single .course-content-rating {
  margin-top: 0;
}
.course-card-4-area.course-list .cr-img img {
  width: 100%;
}
.course-card-4-area.course-list .course-offer-4 {
  bottom: 10px;
  right: 15px;
}
.course-card-4-area.course-list .course-img-4.single {
  flex: 0 0 35%;
}
.course-card-4-area.course-list .course-footer-4 {
  border-top: none;
  padding-top: 0;
}

.carrer-life-area {
  padding: 140px 0;
  position: relative;
}
.carrer-life-area .carrer-shape-1 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.carrer-life-area .carrer-3-desc {
  width: 54%;
  margin-top: 20px;
}
.carrer-life-area .carrer-3-desc p {
  color: #191E24;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 29px;
}
.carrer-life-area .carrer-3-desc .count-success {
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 0;
}
.carrer-life-area .carrer-3-desc .carer-rating-info-3 li i {
  font-size: 12px;
  color: #F8941F;
}
.carrer-life-area .carrer-image-1-conetnt p {
  color: #191E24;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 29px;
  /* 181.25% */
}
.carrer-life-area .carrer-image-1-conetnt .carer-rating-info-3 li {
  display: inline-block;
}
.carrer-life-area .carrer-image-2 {
  position: absolute;
  top: 28%;
  right: 0;
}
.carrer-life-area .carrer-image-2 img {
  border: 6x solid #fff;
}
.carrer-life-area .carer-shape-2 {
  position: absolute;
  top: 0;
  right: 9%;
}
.carrer-life-area .carrer-shape-3 {
  position: absolute;
  bottom: 27%;
  left: 5%;
}
.carrer-life-area .carrer-content-info-3 h6 {
  color: #191E24;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 15px;
}
.carrer-life-area .carrer-content-info-3 h6 i {
  background-color: #808287;
  height: 50px;
  width: 50px;
  display: inline-block;
  line-height: 50px;
  border-radius: 50px;
  font-size: 25px;
  text-align: center;
  margin-right: 15px;
  color: #fff;
}
.carrer-life-area .carrer-content-info-3 h2 {
  color: #191E24;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
}
.carrer-life-area .carrer-content-info-3 h5 {
  color: #191E24;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 10px;
}
.carrer-life-area .carrer-content-info-3 p {
  color: #808287;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}
.carrer-life-area .carrer-content-info-3 .carrer-fexi-info-area {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 30px;
  margin-top: 30px;
}
.carrer-life-area .carrer-content-info-3 .carrer-fexi-info-area .carrer-fexi-card h5 {
  color: #191E24;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 0;
}
.carrer-life-area .carrer-content-info-3 .carrer-fexi-info-area .carrer-fexi-icon {
  margin-bottom: 15px;
}
.carrer-life-area .carrer-content-info-3 .carrer-fexi-info-area .carrer-fexi-icon i {
  font-size: 50px;
}

.student-view-area {
  background-color: #F1F7F7;
  padding: 100px 0;
}

.how-it-work-area-3 {
  padding-top: 140px;
}
.how-it-work-area-3 .play-btn.three {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  height: 120px;
  width: 120px;
  background-color: #191E24;
  cursor: pointer;
}
.how-it-work-area-3 .play-btn.three .video_player_btn {
  line-height: 120px;
}
.how-it-work-area-3 .play-btn.three .video_player_btn a {
  color: #fff;
  font-size: 30px;
}
.how-it-work-area-3 .play-btn.three::after {
  height: 105px;
  width: 105px;
  border: 1px dashed #fff;
}
.how-it-work-area-3 .play-btn.three:hover .video_player_btn a {
  color: #F8941F;
}

.our-program-area {
  background-color: #F1F7F7;
  padding: 100px 0;
}
.our-program-area .pro-btn-3-area {
  text-align: right;
}
.our-program-area .progra-img-area {
  position: relative;
}
.our-program-area .progra-img-area .course-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1010;
  margin-bottom: 0;
  font-size: 32px;
  color: #fff;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
  opacity: 0;
  transition-duration: 400ms;
}
.our-program-area .progra-img-area .progra-img img {
  filter: blur(5px);
}
.our-program-area .progra-img-area::after {
  position: absolute;
  content: "";
  background-color: rgba(25, 30, 36, 0.5);
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 2;
  opacity: 0;
}
.our-program-area .swiper-slide.swiper-slide-active .progra-img-area .progra-img img {
  filter: blur(0);
}
.our-program-area .swiper-slide.swiper-slide-active .progra-img-area::after {
  opacity: 1;
}
.our-program-area .swiper-slide.swiper-slide-active .progra-img-area .course-title {
  opacity: 1;
  visibility: visible;
}

.tuition-fees-area {
  padding-top: 140px;
  padding-bottom: 140px;
  position: relative;
}
.tuition-fees-area .tut-shape-1 {
  position: absolute;
  top: 10%;
  left: 10%;
}
.tuition-fees-area .tutu-fee-img img {
  border-radius: 5px;
}
.tuition-fees-area .tutu-fee-acc-area .accordion-item {
  border: none;
  background: #fff;
  box-shadow: 0px 4px 30px 0px rgba(25, 30, 36, 0.1);
  padding: 0 20px;
  margin-bottom: 20px;
}
.tuition-fees-area .tutu-fee-acc-area .accordion-button {
  border: none;
}
.tuition-fees-area .tutu-fee-acc-area .accordion-collapse.collapse.show {
  border-top: 1px solid #E0E0E0;
}
.tuition-fees-area .tutu-fee-acc-area .accordion-button:not(.collapsed) {
  box-shadow: none;
}
.tuition-fees-area .tutu-fee-acc-area .accordion-button:not(.collapsed) {
  background-color: #fff;
}
.tuition-fees-area .tutu-fee-acc-area .accordion-button {
  color: #191E24;
  font-family: var(--ff-heading);
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.tuition-fees-area .tutu-fee-acc-area .accordion-button:not(.collapsed) {
  color: #6956F9;
}
.tuition-fees-area .tutu-fee-acc-area .accordion-button:focus {
  box-shadow: none;
}
.tuition-fees-area .tutu-fee-acc-area .accordion-body {
  padding-left: 0;
  padding-right: 0;
}
.tuition-fees-area .tutu-fee-acc-area .tuition-fee-ac-body p {
  color: #000;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 20px;
}
.tuition-fees-area .tutu-fee-acc-area .tuition-fee-ac-body ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
}
.tuition-fees-area .tutu-fee-acc-area .tuition-fee-ac-body ul .fee-list {
  color: #191E24;
  font-family: var(--ff-heading);
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.tuition-fees-area .tutu-fee-acc-area .tuition-fee-ac-body ul .fee-cost {
  color: #EF5C72;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.tuition-fees-area .tutu-fee-acc-area .tuition-fee-ac-body ul .bg-navy-1 {
  background: #E4E9FD;
}
.tuition-fees-area .tutu-fee-acc-area .tuition-fee-ac-body ul .bg-gray-1 {
  background: #F2F2F2;
}

.accordion-button::after {
  content: "\e94a";
  background-image: none;
  font-family: "icomoon";
  font-size: 20px;
  font-weight: 400;
  height: 30px;
  width: 30px;
  border: none;
  line-height: 30px;
  border-radius: 50%;
  margin-inline-start: 0;
  text-align: center;
  position: absolute;
  right: 15px;
}
.accordion-button:not(.collapsed)::after {
  background: transparent;
  content: "\e94b";
  font-family: "icomoon";
  font-size: 18px;
  color: var(--gray);
}

.log-regi-area {
  padding: 140px 0;
}
.log-regi-area .log-area {
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: #fff;
  box-shadow: 0px 4px 30px 0px rgba(25, 30, 36, 0.1);
  padding: 60px;
}
.log-regi-area .log-area h2 {
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 30px;
}
.log-regi-area .log-area .password-key {
  position: absolute;
  right: 26px;
  top: 50%;
  cursor: pointer;
  transform: translateY(-50%);
}
.log-regi-area .log-area .auth-desc {
  color: #808287;
  font-family: var(--ff-body);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.log-regi-area .log-area .auth-desc a {
  color: #191E24;
  font-family: var(--ff-body);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.log-regi-area .log-area .form-check-label {
  color: #808287;
  font-family: var(--ff-body);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}
.log-regi-area .log-area .forgot-pass a {
  color: #808287;
  font-family: var(--ff-body);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-decoration: underline;
}
.log-regi-area .rem-forgot-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-control {
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0;
  color: #808287;
  font-family: var(--ff-heading);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.form-control:focus {
  border-color: #6956F9;
  box-shadow: none;
}

.lable-text {
  color: #808287;
  font-size: 20px;
  font-style: normal;
  font-family: var(--ff-heading);
  font-weight: 400;
  line-height: normal;
  background-color: transparent;
}

.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  color: rgba(var(--bs-body-color-rgb), 0.65);
  transform: scale(0.85) translateY(-1.8rem) translateX(0.15rem);
}

.form-floating > .form-control-plaintext ~ label,
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  color: rgba(var(--bs-body-color-rgb), 0.65);
  transform: scale(0.85) translateY(-1.8rem) translateX(0.15rem);
}

.shop-area {
  padding-top: 140px;
  padding-bottom: 110px;
}

.shop-card-single {
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 4px 40px 0px rgba(237, 213, 213, 0.2);
  padding: 40px;
  margin-bottom: 30px;
}
.shop-card-single .shop-img {
  background: linear-gradient(221deg, #E9D1D5 36.7%, #D3C1C2 99%);
  padding: 50px;
  border-radius: 10px;
}
.shop-card-single h4 a {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 5px;
  margin-top: 8px;
  color: #191E24;
  display: block;
  transition-duration: 400ms;
}
.shop-card-single h4:hover a {
  color: #EF5C72;
}
.shop-card-single .auth-info {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
  margin-top: 0;
}
.shop-card-single .auth-info a {
  color: #191E24;
  font-family: var(--ff-body);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 29px;
  margin-left: 8px;
}
.shop-card-single p {
  margin-bottom: 0;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 20px;
}
.shop-card-single p i {
  color: #191E24;
  font-size: 15px;
}
.shop-card-single .course-footer-4 {
  border-top: 1px solid #E0E0E0;
  padding-top: 20px;
  margin-top: 10px;
}
.shop-card-single .course-price p {
  color: #EF5C72;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
  font-family: var(--ff-heading);
  margin-top: 0;
}
.shop-card-single .course-price p span {
  color: #808287;
  font-family: var(--ff-heading);
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-decoration: line-through;
  margin-left: 5px;
}
.shop-card-single .course-buy p {
  margin-bottom: 0;
  margin-top: 0;
}
.shop-card-single .course-buy p a {
  color: #191E24;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  transition-duration: 400ms;
}
.shop-card-single .course-buy p a:hover {
  color: #EF5C72;
}

.checkout-page-area {
  padding-top: 140px;
  padding-bottom: 140px;
}
.checkout-page-area .form-control {
  border: 1px solid #E6E3F1;
  background: #FFF;
  font-size: 14px;
  padding: 11px;
}
.checkout-page-area .form-select {
  border: 1px solid #E6E3F1;
  padding: 13px;
  border-radius: 0;
  font-size: 14px;
  cursor: pointer;
}
.checkout-page-area .checkout-heading h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 40px;
}
.checkout-page-area .form-label {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}
.checkout-page-area .checkout-order-area {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 40px 30px;
}
.checkout-page-area .checkout-order-area h4 {
  margin-bottom: 20px;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.checkout-page-area .checkout-order-area .order-list-check {
  padding-left: 0;
}
.checkout-page-area .checkout-order-area .order-list-check li {
  list-style: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.checkout-page-area .checkout-order-area .order-list-check li .product-list-single {
  display: flex;
  align-items: center;
  color: #2B313E;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
  font-family: var(--ff-body);
  flex: 0 0 70%;
}
.checkout-page-area .checkout-order-area .order-list-check li .product-list-single .order-product {
  flex: 0 0 60px;
  height: 60px;
  margin-right: 15px;
}
.checkout-page-area .checkout-order-area .order-list-header {
  background-color: #6956F9;
  padding: 15px 30px;
}
.checkout-page-area .checkout-order-area .order-list-header .heading-title-pro {
  color: #fff;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.checkout-page-area .checkout-order-area .order-list-info-pro {
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  padding: 15px 0;
}
.checkout-page-area .checkout-order-area .order-price {
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  padding: 15px 0;
  color: #191E24;
  font-family: var(--ff-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.checkout-page-area .checkout-order-area .order-price .pur-price {
  font-family: var(--ff-body);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 29px;
}
.checkout-page-area .payment-gate-sys {
  margin-top: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}
.checkout-page-area .pay-policy-des P {
  margin-top: 15px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.checkout-page-area .post-btn-area {
  text-align: right;
}

.shop-cart-area {
  padding-top: 140px;
}
.shop-cart-area .product-single-img {
  padding: 80px 0;
  border-radius: 10px;
  background: linear-gradient(221deg, #E9D1D5 36.7%, #D3C1C2 99%);
  margin-bottom: 50px;
}

.product-tab-title {
  margin-top: 50px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  padding-bottom: 30px;
}
.product-tab-title .nav-link {
  border: none;
  color: rgba(93, 102, 111, 0.5);
  font-family: var(--ff-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-right: 20px;
}
.product-tab-title .nav-link.active,
.product-tab-title .show > .nav-link {
  color: #191E24;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background-color: #F3F3F3;
}

.product-details-tab-content p {
  margin-top: 30px;
  margin-bottom: 0;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}

.product-info-details-area .product-rating {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.product-info-details-area .product-rating i {
  color: #F8941F;
}
.product-info-details-area .product-rating span {
  font-weight: 700;
}
.product-info-details-area h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.product-info-details-area .pro-auth {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}
.product-info-details-area .pro-auth a {
  color: #191E24;
  font-family: var(--ff-body);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 29px;
}
.product-info-details-area h2 {
  color: #EF5C72;
  font-size: 35px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.product-info-details-area .cart-form {
  display: flex;
  align-items: center;
}
.product-info-details-area .cart-btn {
  border-radius: 3px;
  background: #6956F9;
  color: #fff;
  padding: 10px 15px;
  font-family: var(--ff-heading);
  font-size: 16px;
  border: none;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  transition-duration: 400ms;
}
.product-info-details-area .cart-btn:hover {
  background-color: #F8941F;
}
.product-info-details-area .cart-form .form-control {
  max-width: 3.125rem;
  height: 2.188rem;
  text-align: center;
  font-weight: 500;
  padding: 0.375rem 0.5rem;
  border-bottom: none;
}
.product-info-details-area .cart-form .quantity-button-handler {
  height: 40px;
  width: 40px;
  line-height: 38px;
  border: 1px solid var(--Gray, #808287);
  color: #808287;
  font-size: 1.5rem;
  text-align: center;
  border-radius: 0;
  cursor: pointer;
  transition-duration: 500ms;
}
.product-info-details-area .qty-pro-area {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}
.product-info-details-area .qty-pro-area p {
  color: #1C263A;
  font-family: var(--ff-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
  margin-right: 25px;
}
.product-info-details-area .product-desc {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 30px;
}
.product-info-details-area h5 {
  color: #1C263A;
  font-family: var(--ff-body);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
}
.product-info-details-area h5 span {
  color: #808287;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}

.product-info-details-area {
  padding-left: 10%;
}

.simi-title {
  margin-bottom: 50px;
  text-align: center;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
}

.add-card-alert-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 100px;
  border-top: 2px solid #6956F9;
  background: #F2F2F2;
  padding: 20px;
}
.add-card-alert-area p {
  margin-bottom: 0;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
}
.add-card-alert-area p i {
  color: #F8941F;
  margin-right: 3px;
}
.add-card-alert-area .cart-btn {
  display: inline-block;
  padding: 7px 14px;
  align-items: center;
  color: #FFF;
  font-family: var(--ff-body);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  background-color: #6956F9;
  border-radius: 3px;
  transition-duration: 400ms;
}
.add-card-alert-area .cart-btn:hover {
  background-color: #F8941F;
}

.font-bold {
  font-weight: 700;
}

.product-cart-area {
  padding-top: 140px;
  padding-bottom: 140px;
}
.product-cart-area .cart-pro-img {
  text-align: center;
}
.product-cart-area .cart-pro-img img {
  width: 50px;
}
.product-cart-area .table td,
.product-cart-area .table th {
  vertical-align: middle;
  font-size: 20px;
  padding: 1rem 0.5rem;
  text-align: center;
}

.table {
  border-color: transparent;
}

.table-navy {
  --bs-table-color: #000;
  --bs-table-bg: #6956F9;
  --bs-table-border-color: #c6c7c8;
  --bs-table-striped-bg: #ecedee;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #dfe0e1;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #e5e6e7;
  --bs-table-hover-color: #000;
  color: #fff;
  border-color: transparent;
}

.table th {
  color: #fff;
  font-family: var(--ff-heading);
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.quantity-button-handler {
  cursor: pointer;
}

.cart-table .cart-form {
  display: flex;
  align-items: center;
}
.cart-table .cart-form .form-control {
  width: 50px;
  text-align: center;
  border: none;
}
.cart-table .fav-icon {
  margin-left: 10px;
}
.cart-table .fav-icon i {
  font-size: 14px;
}

.coupon-cart-btn-area {
  margin-top: 100px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  padding-bottom: 30px;
}
.coupon-cart-btn-area .apply-coupon {
  display: flex;
  justify-content: space-between;
  align-items: end;
}
.coupon-cart-btn-area .apply-coupon .coupon-form .cop-form {
  display: flex;
}
.coupon-cart-btn-area .apply-coupon .coupon-form .cop-form .btn-copon {
  border-radius: 5px;
  background: #6956F9;
  color: #fff;
  font-family: var(--ff-body);
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  border: none;
  width: 170px;
  padding: 0 5px;
  transition-duration: 400ms;
  margin-left: 20px;
}
.coupon-cart-btn-area .apply-coupon .coupon-form .cop-form .btn-copon:hover {
  background-color: #F8941F;
}
.coupon-cart-btn-area .apply-coupon .coupon-form .form-control {
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  color: #5D666F;
  font-family: var(--ff-body);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  height: 60px;
}
.coupon-cart-btn-area .update-btn {
  border-radius: 5px;
  background: #6A9CFA;
  color: var(--White, #FFF);
  font-family: var(--ff-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  border: none;
  height: 60px;
  padding: 0 15px;
  transition-duration: 400ms;
}
.coupon-cart-btn-area .update-btn:hover {
  background: #477ee3;
}

.cart-total-card {
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.05);
  padding: 30px;
  margin-top: 30px;
}
.cart-total-card h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 20px;
}
.cart-total-card .cart-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
}
.cart-total-card .cart-list p {
  color: #1C263A;
  font-family: var(--ff-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.cart-total-card .cart-list .cart-tax-pri p {
  color: #1C263A;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.cart-total-card .total-order {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.cart-total-card .total-order h5 {
  margin-bottom: 0;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.cart-total-card .total-order .total-order-desc h6 {
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 0;
}
.cart-total-card .total-order .total-order-desc span {
  color: #808287;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.cart-total-card .pro-btn {
  border-radius: 3px;
  background: #6956F9;
  color: #FFF;
  font-family: var(--ff-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  border: none;
  text-align: center;
  width: 100%;
  padding: 10px;
}

.breadcrumb-area {
  height: 450px;
  background: linear-gradient(180deg, rgba(25, 30, 36, 0.5) 0%, rgba(105, 86, 249, 0.5) 100%);
}
.breadcrumb-area .breadcrumb-conetnt {
  margin-top: 100px;
}
.breadcrumb-area .breadcrumb-conetnt h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.breadcrumb-area .breadcrumb-conetnt p {
  color: #191E24;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 0;
}
.breadcrumb-area .breadcrumb-conetnt .bread-list {
  padding-left: 0;
  margin-bottom: 15px;
}
.breadcrumb-area .breadcrumb-conetnt .bread-list li {
  display: inline-block;
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.breadcrumb-area .breadcrumb-conetnt .bread-list li i {
  font-size: 26px;
  line-height: 1;
  position: relative;
  top: 7px;
}
.breadcrumb-area .grid-filter-area {
  position: absolute;
  bottom: 0;
  z-index: 10;
  left: 0;
  width: 100%;
}
.breadcrumb-area .grid-filter-search {
  background-color: #EF5C72;
  width: 100%;
  padding: 15px 30px;
  border-radius: 5px 5px 0px 0px;
}
.breadcrumb-area .grid-filter-btn p {
  color: #fff;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.breadcrumb-area .filter-btn {
  background: #fff;
  border: none;
  border-radius: 22.5px;
  color: #191E24;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 8px 20px;
  transition-duration: 400ms;
}
.breadcrumb-area .grid-filter-btn-list {
  background: #ECE1E1;
  margin-right: 20px;
  padding: 5px 8px;
  border-radius: 22.5px;
}
.breadcrumb-area .grid-filter-btn-list .grid-btn {
  background: #ECE1E1;
  border: none;
  border-radius: 22.5px;
  color: #191E24;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 8px 20px;
  transition-duration: 400ms;
}
.breadcrumb-area .grid-filter-btn-list .grid-btn i {
  font-size: 12px;
}
.breadcrumb-area .grid-filter-btn-list .grid-btn:hover {
  background-color: #fff;
}
.breadcrumb-area .grid-filter-btn-list .list-btn {
  background: #ECE1E1;
  border: none;
  border-radius: 22.5px;
  color: #191E24;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 8px 20px;
  transition-duration: 400ms;
}
.breadcrumb-area .grid-filter-btn-list .list-btn i {
  font-size: 12px;
}
.breadcrumb-area .grid-filter-btn-list .list-btn:hover {
  background-color: #fff;
}

.breadcrumb-area-2 {
  height: 500px;
}
.breadcrumb-area-2 .breadcrumb-conetnt {
  margin-top: 60px;
}
.breadcrumb-area-2 .breadcrumb-conetnt .bread-list {
  padding-left: 0;
}
.breadcrumb-area-2 .breadcrumb-conetnt .bread-list li {
  display: inline-block;
  color: #FFF;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.breadcrumb-area-2 .breadcrumb-conetnt .bread-list li i {
  font-size: 26px;
  position: relative;
  top: 7px;
}
.breadcrumb-area-2 .breadcrumb-conetnt .bread-list li a {
  display: inline-block;
  color: #FFF;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.breadcrumb-area-2 .breadcrumb-conetnt h4 {
  color: #fff;
  font-size: 30px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-top: 5px;
}

.searc-filed-cart-area {
  border-radius: 0px 0px 5px 5px;
  background: #FCF4F4;
  padding: 30px 20px;
  position: absolute;
  bottom: -135px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 101010;
  display: none;
  transition-duration: 500ms;
  width: 67%;
}
.searc-filed-cart-area .search-filter-box {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 30px;
}
.searc-filed-cart-area .search-filter-box .form-control {
  font-size: 12px;
  color: #828282;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  width: 100%;
  height: 40px;
}

.course-details-area {
  padding-top: 140px;
  padding-bottom: 140px;
}
.course-details-area .course-detials-tab {
  margin-top: 40px;
}
.course-details-area .course-detials-tab .course-tab-title {
  border-bottom: 1px solid #BDBDBD;
  padding-bottom: 20px;
  margin-bottom: 50px;
}
.course-details-area .course-detials-tab .nav-item {
  margin-right: 100px;
}
.course-details-area .course-detials-tab .nav-pills .nav-link {
  color: var(--Black, #191E24);
  font-family: var(--ff-heading);
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.course-details-area .course-detials-tab .nav-pills .nav-link i {
  margin-right: 5px;
}
.course-details-area .course-detials-tab .nav-pills .nav-link.active,
.course-details-area .course-detials-tab .nav-pills .show > .nav-link {
  color: var(--Gray, #808287);
  font-family: var(--ff-heading);
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background-color: transparent;
}
.course-details-area .other-ins-area {
  border-top: 1px solid #BDBDBD;
  padding-top: 50px;
}
.course-details-area .other-ins-area .other-heading {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 40px;
}
.course-details-area .other-ins-area .inst-member-info {
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 4px 30px 0px rgba(25, 30, 36, 0.1);
  padding: 15px;
  margin-bottom: 30px;
}
.course-details-area .other-ins-area .inst-member-info .inst-img {
  flex: 0 0 135px;
  width: 135px;
  margin-right: 40px;
}
.course-details-area .other-ins-area .inst-member-info .inst-img img {
  border-radius: 10px;
}
.course-details-area .other-ins-area .inst-member-info .inst-info h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.course-details-area .other-ins-area .inst-member-info .inst-info p {
  color: #191E24;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.course-details-area .other-ins-area .inst-member-info .inst-info .inst-social-list {
  padding-left: 0;
}
.course-details-area .other-ins-area .inst-member-info .inst-info .inst-social-list li {
  display: inline-block;
  margin-right: 10px;
}
.course-details-area .other-ins-area .inst-member-info .inst-info .inst-social-list li a {
  color: #BDBEC5;
  transition-duration: 400ms;
}
.course-details-area .other-ins-area .inst-member-info .inst-info .inst-social-list li:hover a {
  color: #6956F9;
}
.course-details-area .course-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 50px;
}
.course-details-area .course-title h2 {
  color: #191E24;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.course-details-area .course-info-card-area {
  display: flex;
  justify-content: space-between;
}
.course-details-area .course-info-card-area .course-info-card {
  flex: 0 0 25%;
}
.course-details-area .course-info-card-area .course-info-card .course-desc {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-top: 15px;
}
.course-details-area .course-info-card-area .course-info-card h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.course-details-area .course-info-card-area .course-info-card .course-auth-info-4 .auth-img {
  margin-right: 15px;
  border-radius: 50%;
  height: 35px;
  width: 35px;
  flex: 0 0 35px;
}
.course-details-area .course-info-card-area .course-info-card .course-auth-info-4 p {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 0;
}
.course-details-area .course-info-card-area .course-info-card .course-auth-info-4 p a {
  color: #191E24;
  font-family: var(--ff-body);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 29px;
  margin-left: 5px;
}
.course-details-area .course-bg-img {
  margin-top: 50px;
}
.course-details-area .course-bg-img img {
  border-radius: 10px;
}
.course-details-area .course-rating {
  margin-bottom: 0;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-top: 15px;
}
.course-details-area .course-rating i {
  color: #F8941F;
}
.course-details-area .course-details-tab-content h4 {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 20px;
}
.course-details-area .course-details-tab-content p {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 29px;
  margin-bottom: 50px;
}

.featured-course-4-area .course-card-4-area .auth-info-4 .auth-img {
  margin-right: 15px;
  border-radius: 50%;
}

.faq-area.course-details {
  padding-top: 20px;
  padding-bottom: 0;
}

.sidebar-content-single-area .side-bar-video-area {
  margin-bottom: 40px;
  border-radius: 10px;
  background: #fff;
  padding: 20px;
  box-shadow: 0px 4px 40px 0px rgba(237, 213, 213, 0.2);
}
.sidebar-content-single-area .side-bar-video-area .side-video-img {
  position: relative;
}
.sidebar-content-single-area .side-bar-video-area .side-video-img img {
  border-radius: 10px;
}
.sidebar-content-single-area .side-bar-video-area .side-video-img::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(25, 30, 36, 0.7);
  z-index: 2;
  border-radius: 10px;
}
.sidebar-content-single-area .side-bar-video-area .play-btn.course {
  position: absolute;
  z-index: 1010;
  height: 80px;
  width: 80px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.sidebar-content-single-area .side-bar-video-area .play-btn.course::after {
  display: none;
}
.sidebar-content-single-area .side-bar-video-area .play-btn.course .video_player_btn a {
  font-size: 30px;
  line-height: 85px;
}
.sidebar-content-single-area .side-bar-video-area .enroll-course-content .enroll-heading {
  margin-top: 40px;
}
.sidebar-content-single-area .side-bar-video-area .enroll-course-content .enroll-heading a {
  display: block;
  margin-bottom: 15px;
}
.sidebar-content-single-area .side-bar-video-area .enroll-course-content .enroll-heading p {
  color: #808287;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.sidebar-content-single-area .side-bar-video-area .enroll-course-content .course-info-list {
  padding-left: 0;
  margin-top: 40px;
}
.sidebar-content-single-area .side-bar-video-area .enroll-course-content .course-info-list li {
  border-top: 1px solid #E0E0E0;
  padding-top: 20px;
  padding-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  list-style: none;
  color: #808287;
  font-family: var(--ff-heading);
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.sidebar-content-single-area .side-bar-video-area .enroll-course-content .course-info-list li:last-child {
  padding-bottom: 0;
}

/* Fonts */
:root {
  --ff-heading: "Jost", sans-serif;
  --ff-body: "Poppins", sans-serif;
}

* {
  margin: 0;
  padding: 0;
}

html,
body {
  scroll-behavior: smooth;
  font-family: var(--ff-body);
  font-size: 1rem;
  font-weight: 500;
  overflow-x: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--ff-heading);
  line-height: 1.3;
  font-weight: 600;
  color: #191E24;
}

p {
  font-size: 1rem;
  font-family: var(--ff-body);
  color: #808287;
}

a,
a:hover,
a:focus {
  transition-duration: 350ms;
  text-decoration: none;
  outline: 0 solid transparent;
  font-family: var(--ff-heading);
}

ul,
ol {
  margin: 0;
  padding-left: 1rem;
}

ul li,
ol li {
  text-decoration: none;
}

ul li:hover,
ul li:focus,
ol li:hover,
ol li:focus {
  text-decoration: none;
}

img {
  max-width: 100%;
}

.mt-20 {
  margin-top: 20px;
}

.padding-150 {
  padding-top: 150px;
  padding-bottom: 150px;
}

.padding-140-0 {
  padding-top: 140px;
  padding-bottom: 0;
}

.padding-100-150 {
  padding-top: 100px;
  padding-bottom: 150px;
}

.padding-bottom-100 {
  padding-bottom: 100px;
}

.ml-auto {
  margin-left: auto;
}

.box-shadow1 {
  box-shadow: 0px 4px 4px 0px rgba(239, 233, 233, 0.2);
}

.border-radius1 {
  border-radius: 0px 0px 10px 10px;
}

.relative {
  position: relative;
}

.border--primary {
  border-color: #F8941F !important;
}

.border--second {
  border-color: #EF5C72 !important;
}

.border--navy {
  border-color: #6956F9 !important;
}

.row > * {
  padding-right: 15px;
  padding-left: 15px;
}

.section-title-area {
  margin-bottom: 60px;
  position: relative;
  z-index: 10;
}
.section-title-area h6 {
  border-radius: 31px;
  background: #F9F2F2;
  color: #808287;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: inline-block;
  padding: 10px 20px;
}
.section-title-area h2 {
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
}

.text--primary {
  color: #F8941F;
}

.text--second {
  color: #EF5C72;
}

.text--dark {
  color: #191E24;
}

.text--navy {
  color: #6956F9;
}

.bg--primary {
  background-color: #F8941F;
}

.bg--second {
  background-color: #EF5C72;
}

.bg--navy {
  background-color: #6956F9;
}

.bg--dark {
  background-color: #191E24;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.auth-btn {
  background-color: #6956F9;
  padding: 15px;
  color: #FFF;
  font-family: var(--ff-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  border-radius: 5px;
  border: none;
  position: relative;
  overflow: hidden;
}
.auth-btn::after {
  transition-duration: 400ms;
  position: absolute;
  width: 0;
  height: 100%;
  top: 0;
  content: "";
  background-color: rgba(255, 255, 255, 0.15);
  left: -10%;
}
.auth-btn:hover::after {
  width: 120%;
}

.inner-btn {
  background-color: #6956F9;
  color: #fff;
  font-size: 25px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  padding: 10px 20px;
  border-radius: 65px;
  transition-duration: 400ms;
  position: relative;
  overflow: hidden;
}
.inner-btn::after {
  transition-duration: 400ms;
  position: absolute;
  width: 0;
  height: 100%;
  top: 0;
  content: "";
  background-color: rgba(255, 255, 255, 0.15);
  left: -10%;
}
.inner-btn:hover::after {
  width: 120%;
}

.load-btn {
  background-color: #191E24;
  padding: 10px 20px;
  border-radius: 3px;
  color: #fff;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  position: relative;
  overflow: hidden;
}
.load-btn::after {
  transition-duration: 400ms;
  position: absolute;
  width: 0;
  height: 100%;
  top: 0;
  content: "";
  background-color: rgba(255, 255, 255, 0.15);
  left: -10%;
}
.load-btn i {
  margin-right: 2px;
}
.load-btn:hover::after {
  width: 120%;
}

.btn-edu {
  padding: 10px 25px;
  display: inline-block;
  border-radius: 36px;
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  position: relative;
  overflow: hidden;
}
.btn-edu::after {
  transition-duration: 400ms;
  position: absolute;
  width: 0;
  height: 100%;
  top: 0;
  content: "";
  background-color: rgba(255, 255, 255, 0.15);
  left: -10%;
}
.btn-edu:hover {
  font-size: 16px;
  font-weight: 400;
  padding: 10px 25px;
  color: #fff;
}
.btn-edu:hover::after {
  width: 120%;
}

.ml-1 {
  margin-left: 5px;
}

.mt-30 {
  margin-top: 30px;
}

.mr-3 {
  margin-right: 30px;
}

.mb-30 {
  margin-bottom: 30px;
}

.pro-btn-3 {
  background-color: #191E24;
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 10px 25px;
  border-radius: 36px;
  display: inline-block;
  position: relative;
  overflow: hidden;
}
.pro-btn-3::after {
  transition-duration: 400ms;
  position: absolute;
  width: 0;
  height: 100%;
  top: 0;
  content: "";
  background-color: rgba(255, 255, 255, 0.15);
  left: -10%;
}
.pro-btn-3:hover::after {
  width: 120%;
}

.login-btn-3 {
  background-color: #F8941F;
  color: #fff;
  padding: 4px 20px;
  display: inline-block;
  border-radius: 3px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-left: 30px;
}

.btn-1 {
  background-color: #F8941F;
  padding: 10px 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  border-radius: 36px;
  transition-duration: 500ms;
  position: relative;
  overflow: hidden;
  display: inline-block;
}
.btn-1::after {
  transition-duration: 400ms;
  position: absolute;
  width: 0;
  height: 100%;
  top: 0;
  content: "";
  background-color: rgba(255, 255, 255, 0.15);
  left: -10%;
}
.btn-1 span {
  color: #fff;
  margin-left: 8px;
  position: relative;
  top: 1px;
}
.btn-1:hover::after {
  width: 120%;
}

.btn-4 {
  color: var(--white, #FFF);
  font-family: var(--ff-heading);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  border-radius: 3px;
  border: 1px solid #fff;
  padding: 15px 30px;
  display: inline-block;
  position: relative;
  overflow: hidden;
}
.btn-4::after {
  transition-duration: 400ms;
  position: absolute;
  width: 0;
  height: 100%;
  top: 0;
  content: "";
  background-color: rgba(255, 255, 255, 0.15);
  left: -10%;
}
.btn-4:hover::after {
  width: 120%;
}

.price-btn {
  border-radius: 36px;
  background: #191E24;
  display: block;
  color: #fff;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 10px 25px;
  text-align: center;
  transition-duration: 500ms;
}
.price-btn:hover {
  background-color: #EF5C72;
}

.bg-img {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.bg-fixed {
  background-attachment: fixed;
}

.bg-overlay {
  position: relative;
  z-index: 1;
}

.bg-overlay::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background-color: rgba(241, 247, 247, 0.5);
  top: 0;
  left: 0;
  z-index: -1;
}

.bg-overlay-2 {
  position: relative;
  z-index: 1;
}

.bg-overlay-2::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background-color: rgba(26, 51, 49, 0.9);
  top: 0;
  left: 0;
  z-index: -1;
}

.bg-overlay-3 {
  position: relative;
  z-index: 1;
}

.bg-overlay-3::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background-color: rgba(25, 30, 36, 0.7);
  top: 0;
  left: 0;
  z-index: -1;
}

.bg-overlay-4 {
  position: relative;
  z-index: 1;
}

.bg-overlay-4::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background-color: rgba(25, 30, 36, 0.5);
  top: 0;
  left: 0;
  z-index: -1;
}

.login-btn {
  background-color: #F8941F;
  height: 33px;
  width: 90px;
  display: inline-block;
  text-align: center;
  border-radius: 3px;
  color: #fff !important;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  font-family: var(--ff-heading);
  margin-left: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-btn span {
  margin-right: 5px;
  position: relative;
  top: 1px;
  font-size: 13px;
}

.preloader {
  background-color: #f7f7f7;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999999;
  transition: 0.6s;
  margin: 0 auto;
}

.preloader .preloader-circle {
  width: 100px;
  height: 100px;
  position: relative;
  border-style: solid;
  border-width: 1px;
  border-top-color: #ed078b;
  border-bottom-color: transparent;
  border-left-color: transparent;
  border-right-color: transparent;
  z-index: 10;
  border-radius: 50%;
  box-shadow: 0 1px 5px 0 rgba(35, 181, 185, 0.15);
  background-color: #fff;
  animation: zoom 2000ms infinite ease;
  transition: 0.6s;
}

.preloader .preloader-img {
  position: absolute;
  top: 50%;
  z-index: 200;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-align: center;
  display: inline-block;
  transform: translateY(-50%);
  padding-top: 6px;
  transition: 0.6s;
}

.preloader .preloader-img img {
  max-width: 80px;
}

.preloader .pere-text strong {
  font-weight: 800;
  color: #dca73a;
  text-transform: uppercase;
}

@keyframes zoom {
  0% {
    transform: rotate(0deg);
    transition: 0.6s;
  }
  100% {
    transform: rotate(360deg);
    transition: 0.6s;
  }
}
#scrollToTop {
  position: fixed;
  z-index: 999;
  bottom: 20%;
  right: 0;
  transition: all 500ms;
  width: 2rem;
  height: 2rem;
  background-color: #F8941F;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem 0 0 0.25rem;
  color: #ffffff;
  cursor: pointer;
  transform: translateX(0);
}
#scrollToTop i {
  transform: rotate(-90deg);
}

#scrollToTop.scrolltop-show {
  transform: translateX(0);
}

#scrollToTop.scrolltop-hide {
  transform: translateX(100px);
}

.zoom--ani {
  transform: translateZ(0) scale(1, 1);
  animation: increase 60s linear 10ms infinite;
  transition: all 0.2s ease-in-out;
}
@keyframes increase {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.7);
  }
}

.heading-btn-4 a {
  color: #191E24;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  border-radius: 3px;
  border: 1px solid #6956F9;
  padding: 15px 30px;
  display: inline-block;
  position: relative;
  overflow: hidden;
  z-index: 10;
  transition-duration: 400ms;
}
.heading-btn-4 a::after {
  transition-duration: 400ms;
  position: absolute;
  width: 0;
  height: 100%;
  top: 0;
  content: "";
  background-color: #6956F9;
  left: -10%;
  z-index: -1;
}
.heading-btn-4 a:hover {
  color: #fff;
}
.heading-btn-4 a:hover::after {
  width: 120%;
}

.heading-title-4 {
  margin-bottom: 60px;
}
.heading-title-4 h6 {
  border-radius: 31px;
  background: #F9F2F2;
  color: #808287;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: inline-block;
  padding: 10px 20px;
}
.heading-title-4 h3 {
  color: #191E24;
  font-size: 50px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  text-transform: capitalize;
  margin-bottom: 0;
}

.heading-btn-4 {
  text-align: right;
}

/*** contact form error handling ***/
.contact-activation .error-handling-messages {
  width: 100% !important;
  margin-top: 15px !important;
}

.contact-activation label.error {
  color: red;
  font-size: 13px;
  font-weight: normal;
  margin: 5px 0 0 0;
  text-align: left;
  display: block;
  margin-bottom: 10px;
}

.contact-activation #c-loader,
.contact-activation #loader {
  display: none;
  margin-top: 10px;
}

.contact-activation #c-loader i,
.contact-activation #loader i {
  font-size: 30px;
  font-size: calc-rem-value(30);
  color: #1A9120;
  display: inline-block;
  animation: rotating linear 2s infinite;
}

.contact-activation #success,
.contact-activation #c-success,
.contact-activation #c-error,
.contact-activation #error {
  width: 100%;
  color: #fff;
  padding: 5px 10px;
  font-size: 16px;
  text-align: center;
  display: none;
}

@media (max-width: 767px) {
  .contact-activation #success,
  .contact-activation #c-success,
  .contact-activation #c-error,
  .contact-activation #error {
    font-size: 15px;
  }
}
.contact-activation #c-success,
.contact-activation #success {
  background-color: #009A00;
  border-left: 5px solid green;
  margin-bottom: 5px;
}

.contact-activation #c-error,
.contact-activation #error {
  background-color: #FF1A1A;
  border-left: 5px solid red;
}
@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.rotating {
  animation: rotating 5s alternate infinite;
}

@media (min-width: 1366px) and (max-width: 1550px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1200px;
  }
  .searc-filed-cart-area {
    width: 85.5%;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::before {
    right: 15%;
    bottom: -67%;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::after {
    left: 15%;
    bottom: -67%;
  }
  .wel-img-2 {
    width: 70%;
  }
  .fav-course-area-3 .tab-btn-img-text h4 {
    font-size: 18px;
  }
  .fav-course-area-3 .tab-btn-img-text p {
    font-size: 12px;
  }
  .fav-course-area-3 .course-tab-heading .nav-link {
    margin: 0 5px;
  }
  .knowledge-cta-area-2 .knowledge-per-img {
    left: 1%;
  }
  .hero-area-4 .hero-content-text h2 {
    font-size: 51px;
  }
  .hero-area-2 .shape-box {
    top: 54%;
  }
  .hero-area-2 .hero-2-shape-3 {
    top: 15%;
    right: 0%;
  }
  .top-categories-area-4 .cate-single-card-4 .cate-content-text-4 h3 {
    font-size: 18px;
  }
  .top-categories-area-4 .cate-single-card-4 {
    padding: 15px 10px;
  }
  .top-categories-area-4 .cate-single-card-4 .cate-content-text-4 .cate-icon-4 i {
    font-size: 20px;
  }
  .top-categories-area-4 .cate-single-card-4 .cate-content-text-4 .cate-icon-4 {
    margin-right: 15px;
  }
  .featured-course-4-area .course-card-4-area .course-img-4 .course-sub-title h6 {
    font-size: 22px;
  }
  .single-counter-card .counter-info h2 {
    font-size: 70px;
    line-height: 1.4;
  }
  .counter-up-area .row > * {
    padding-right: 15px;
    padding-left: 15px;
  }
  .hero-content-text h2 {
    font-size: 63px;
    line-height: 90px;
  }
  .hero-area .shape-1 {
    left: 0;
    top: 20%;
  }
  .hero-area .shape-1 img {
    width: 70%;
  }
  .popular-cata-area .single-cate-card .single-cate-content {
    padding: 40px 20px;
  }
  .about-us-area .about-us-title-img h2 {
    font-size: 43px;
  }
  .about-us-area .about-us-title-img .img-badge-text p {
    font-size: 14px;
  }
  .about-us-area .about-us-title-img .img-badge-text p a {
    font-size: 17px;
  }
  .about-us-area .about-us-title-img .about-img .about-img-2 {
    top: -85px;
    right: -9%;
  }
  .popular-categories-slider-area .cate-slider-area {
    transform: translateY(-40%);
  }
  .cate-single-slier h3 {
    font-size: 22px;
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-icon {
    flex: 0 0 100px;
    height: 100px;
    width: 100px;
    line-height: 100px;
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-icon span {
    font-size: 32px;
    line-height: 100px;
  }
  .section-title-area h2 {
    font-size: 44px;
  }
  .upcoming-event-area .single-event-card:hover {
    margin-left: 70px;
  }
  .certificate-area .certi-title-area h2 {
    font-size: 45px;
  }
  .certificate-area .gra-shape-2 {
    left: 9%;
  }
  .faq-area .faq-content-area .accordion-button {
    font-size: 21px;
  }
  .certificate-area .gra-shape-1 {
    top: 8%;
    left: 13%;
  }
  .knowledge-cta-area-2 .knowledge-image::after {
    left: 19.5%;
  }
  .popular-cate-2-area .cate-2-shape {
    right: 0;
  }
  .learn-skill-area .skill-content-text {
    padding-right: 15%;
  }
  .learn-skill-area .skill-content-text h2 {
    font-size: 48px;
  }
}
@media (min-width: 1200px) and (max-width: 1365px) {
  .hero-area .wel-shape-2 img {
    width: 73%;
  }
  .hero-area-4 .hero-content-text {
    margin-top: 0;
  }
  .client-area-3 .client-nav li img {
    width: 100%;
  }
  .hero-area-4 .hero-content-text {
    margin-top: 30px;
  }
  .hero-area-4 .hero-content-text h2 {
    font-size: 50px;
  }
  .fav-course-area-3 .tab-btn-img-text .tab-btn-img {
    flex: 0 0 36px;
    margin-right: 8px;
  }
  .fav-course-area-3 .tab-btn-img-text p {
    font-size: 11px;
    flex: 0 0 35%;
  }
  .fav-course-area-3 .tab-btn-img-text .course-title-4 {
    font-size: 16px;
  }
  .fav-course-area-3 .course-tab-heading .nav-link {
    margin: 0 5px;
  }
  .fav-course-area-3 .course-content-3-area .course-content-info .course-du-card p {
    font-size: 13px;
  }
  .fav-course-area-3 .course-content-3-area .course-content-info .course-du-card {
    padding: 20px 15px;
  }
  .our-program-area .progra-img-area .course-title {
    font-size: 28px;
  }
  .fav-course-area-3 .course-tab-heading li {
    flex: 0 0 25%;
  }
  .cate-single-slier h3 {
    font-size: 20px;
  }
  .hero-area-2 .hero-content-text h2 {
    font-size: 70px;
    line-height: 90px;
  }
  .hero-area-2 .hero-content-text {
    margin-top: 0;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::after {
    left: 14%;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::before {
    right: 14%;
  }
  .hero-area .wel-shape-2 {
    right: 13%;
    top: 30%;
  }
  .hero-img-area {
    text-align: center;
  }
  .hero-img-area img {
    width: 85%;
  }
  .hero-area .shape-1 {
    position: absolute;
    top: 23%;
    left: -2%;
  }
  .hero-area .shape-1 img {
    width: 50%;
  }
  .hero-area .wel-shape-3 img {
    width: 55%;
  }
  .hero-area .wel-shape-3 {
    top: 30%;
  }
  .hero-area .wel-shape-7 img {
    width: 70%;
  }
  .hero-area .wel-shape-7 {
    right: 0;
    top: 23%;
  }
  .hero-area .wel-shape-3 {
    right: 13%;
  }
  .wel-shape-5 img {
    width: 80%;
  }
  .carrer-life-area .carrer-shape-3 {
    display: none;
  }
  .hero-area .wel-shape-5 {
    left: -7%;
  }
  .tuition-fees-area .tut-shape-1 {
    left: 0%;
  }
  .hero-area .wel-shape-4 {
    top: 25%;
    left: -1%;
  }
  .about-us-area .about-us-title-img .about-img .about-img-2 {
    right: -8%;
  }
  .hero-area .wel-shape-4 img {
    width: 70%;
  }
  .popular-cata-area .single-cate-card .single-cate-content {
    padding: 30px 12px;
  }
  .about-us-area .about-shape-3 img {
    width: 70%;
  }
  .popular-categories-slider-area .cate-slider-area .cate-single-slier h3 {
    font-size: 20px;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text {
    margin-right: 0;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-img {
    flex: 0 0 45%;
  }
  .section-title-area h2 {
    font-size: 37px;
  }
  .section-title-area h6 {
    font-size: 18px;
  }
  .client-area {
    padding-bottom: 80px;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text h3 {
    font-size: 22px;
  }
  .blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content h3 {
    font-size: 22px;
  }
  .blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content p {
    font-size: 14px;
    line-height: 25px;
  }
  .client-area .clinet-heading-review-area .clinet-slider-area .client-info-meta p {
    font-size: 13px;
    line-height: 24px;
  }
  .certificate-area .gra-shape-3 {
    bottom: 12%;
    left: 25%;
  }
  .play-btn {
    height: 120px;
    width: 120px;
  }
  .play-btn .video_player_btn {
    line-height: 120px;
  }
  .play-btn::after {
    height: 105px;
    width: 105px;
  }
  .upcoming-event-area .up-shape-1 {
    left: 0%;
  }
  .client-area-home-2 .client-active-2 .client-info-meta-home-2 p {
    font-size: 15px;
    line-height: 25px;
  }
  .faq-area .faq-content-area .accordion-button {
    font-size: 20px;
  }
  .knowledge-cta-area-2 .knowledge-image::after {
    left: 17.5%;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-conetnt-card-2 h3 {
    font-size: 25px;
  }
  .knowledge-cta-area-2 .curbe-shape-cta-2 {
    bottom: -1%;
  }
  .popular-cate-2-area .cate-2-shape {
    right: 0;
  }
  .certificate-area .gra-shape-1 {
    top: 9%;
    left: 5%;
  }
  .certificate-area .certi-title-area h2 {
    font-size: 46px;
  }
  .certificate-area .gra-cert-image-area .certi-img-2 {
    top: 54%;
  }
  .client-area-home-2 .client-image-home-2::after {
    left: -36%;
  }
  .knowledge-cta-area-2 .knowledge-cta-content h2 {
    font-size: 46px;
  }
  .knowledge-cta-area-2 .knowledge-per-img {
    left: -2%;
  }
  .hero-area-2 .hero-2-shape-3 {
    top: 16%;
    right: -8%;
  }
  .hero-area-2 .hero-2-shape-3 img {
    width: 60%;
  }
  img.wel-img-2 {
    width: 71%;
  }
  .hero-area-2 .bag-shape {
    left: -12%;
    bottom: -22%;
    z-index: 1;
  }
  .hero-area-2 .bag-shape img {
    width: 70%;
  }
  .single-photograph .photo-title-area h2 {
    font-size: 68px;
    line-height: 88px;
  }
  .carrer-life-area .carrer-content-info-3 h2 {
    font-size: 42px;
  }
  .carrer-life-area .carrer-image-2 {
    right: -13%;
  }
  .carrer-life-area .carer-shape-2 {
    right: -6%;
  }
  .single-photograph .photo-title-area {
    padding: 100px 0;
  }
  .single-photograph {
    height: 800px;
  }
  .hero-content-text h2 {
    font-size: 56px;
    line-height: 78px;
  }
  .hero-area {
    height: 800px;
  }
  .about-us-area .about-us-title-img h2 {
    font-size: 41px;
  }
  .about-us-area .about-us-title-img .img-badge-text p {
    font-size: 12px;
  }
  .about-us-area .about-us-title-img .img-badge-text p a {
    font-size: 14px;
  }
  .about-us-area .about-desc-info-area .about-content-card-area .about-single-card p {
    font-size: 15px;
  }
  .about-us-area .about-desc-info-area p {
    font-size: 15px;
  }
  .popular-categories-slider-area .cate-slider-area {
    transform: translateY(-39%);
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-icon {
    flex: 0 0 100px;
    height: 100px;
    width: 100px;
    line-height: 100px;
  }
  .searc-filed-cart-area {
    bottom: -134px;
    width: 92.5%;
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-icon span {
    font-size: 32px;
    line-height: 100px;
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-icon {
    margin-right: 20px;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text h2 {
    font-size: 41px;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text p {
    font-size: 15px;
  }
  .client-footer .user-icon {
    height: 45px;
    flex: 0 0 45px;
    width: 45px;
    line-height: 55px;
    line-height: 48px;
    font-size: 28px;
    margin-right: 10px;
  }
  .client-footer .client-info h4 {
    font-size: 15px;
  }
  .client-area .clinet-heading-review-area .clinet-slider-area .client-info-meta .clinet-img img {
    height: 90px;
    width: 90px;
  }
  .client-area .clinet-heading-review-area .clinet-slider-area .client-info-meta p {
    margin-top: 25px;
  }
  .client-area .clinet-heading-review-area .clinet-heading p {
    font-size: 15px;
    line-height: 26px;
  }
  .hero-area .wel-shape-course-title {
    top: 18%;
    left: 5%;
  }
  .hero-area .wel-shape-course-title .shape-title-course p {
    font-size: 12px;
  }
  .hero-area .wel-shape-course-title .shape-title-course {
    left: 52%;
  }
  .learn-skill-area .skill-content-text {
    padding-right: 15%;
  }
  .learn-skill-area .skill-content-text h2 {
    font-size: 46px;
  }
  .single-counter-card .counter-info h2 {
    font-size: 66px;
    line-height: 85px;
  }
  .single-counter-card .counter-info {
    padding: 45px 30px;
  }
  .footer-wrap .footer-content .footer-desc {
    font-size: 22px;
  }
  .popular-cate-2-area .cate-price-card .cate-price-body h2 {
    font-size: 46px;
  }
  .certificate-area .gra-shape-2 {
    top: 5%;
    left: 1%;
    z-index: 9;
  }
}
@media (min-width: 1367px) and (max-width: 1500px) {
  .hero-area .wel-shape-2 img {
    width: 75%;
  }
  .hero-area .wel-shape-2 {
    right: -3%;
  }
  .hero-area .shape-1 img {
    width: 38%;
  }
  .hero-area .shape-1 {
    top: 17%;
  }
  .hero-area .wel-shape-3 img {
    width: 70%;
  }
  .hero-area .wel-shape-3 {
    top: 13%;
  }
  .hero-area .wel-shape-4 {
    left: -1%;
  }
  .hero-area .wel-shape-7 {
    right: 3%;
    top: 20%;
  }
  .hero-area .wel-shape-7 img {
    width: 80%;
  }
}
@media (min-width: 992px) and (max-width: 1119px) {
  .menu-area- {
    padding: 30px 15px;
  }
  .hero-content-text h2 {
    font-size: 52px;
    line-height: 73px;
  }
  .header-area .navbar-nav li > a {
    padding: 0.1rem 0.5rem;
  }
  .certificate-area .gra-shape-2 {
    left: -3%;
  }
  .hero-area .wel-shape-course-title .shape-title-course p {
    font-size: 12px;
  }
  .hero-area .wel-shape-course-title {
    top: 21%;
    left: 0%;
  }
  .hero-area .wel-shape-4 {
    display: none;
  }
  .certificate-area .gra-shape-1 {
    top: 10%;
    left: 4%;
  }
  .certificate-area .gra-shape-4 {
    right: 0%;
  }
  img.wel-img-2 {
    width: 72%;
  }
  .play-btn {
    height: 110px;
    width: 110px;
  }
  .play-btn::after {
    height: 95px;
    width: 95px;
  }
  .certificate-area .certi-single-card .certi-border .certi-icon {
    height: 70px;
    width: 70px;
    line-height: 70px;
  }
  .play-btn .video_player_btn a {
    line-height: 115px;
  }
  .upcoming-event-area .single-event-card .event-price {
    margin-right: 30px;
  }
  .top-header.home-2 .header-top-home-2 {
    padding: 10px;
  }
  .hero-area-2 .bag-shape {
    display: none;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text h6 {
    font-size: 14px;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text p {
    font-size: 12px;
  }
  .header-area.home-2 {
    top: 60px;
  }
  .header-area.home-2 .cart-search-area {
    padding: 8px 20px;
  }
  .hero-area-2 .hero-2-shape-2 {
    bottom: 8%;
  }
  .hero-area-2 {
    height: 800px;
  }
  .navbar-brand img {
    width: 140px;
  }
  .hero-area-2 .hero-2-shape-1 {
    top: 15%;
    left: 2%;
  }
  .care-program-area .single-care-card .care-card-img-content .care-content-text p {
    font-size: 15px;
    line-height: 26px;
  }
  .care-program-area .single-care-card .care-card-img-content .care-content-text .care-count-info p {
    font-size: 18px;
  }
  .popular-cate-2-area .cate-price-card .cate-price-body h2 {
    font-size: 33px;
  }
  .popular-cate-2-area .cate-price-card {
    padding: 40px 20px;
  }
  .popular-cate-2-area .cate-price-card .cate-price-body h6 {
    font-size: 21px;
  }
  .certificate-area .certi-title-area h2 {
    font-size: 37px;
  }
  .certificate-area .certi-single-card .certi-card-info p {
    font-size: 14px;
    line-height: 25px;
  }
  .certificate-area .certi-single-card .certi-card-info h4 {
    font-size: 22px;
  }
  .popular-cate-2-area .cate-price-card .cate-price-body ul li {
    font-size: 14px;
  }
  .hero-area-2 .hero-2-shape-3 {
    top: 15%;
    right: -12%;
  }
  .hero-area-2 .hero-2-shape-3 img {
    width: 50%;
  }
  .searc-filed-cart-area {
    width: 94.5%;
  }
  .hero-img-area {
    position: absolute;
    bottom: 0;
    right: -14%;
    z-index: 1;
  }
  .hero-area .wel-shape-7 {
    top: 25%;
  }
  .wel-shape-2 img {
    width: 65%;
  }
  .hero-img-area img {
    width: 80%;
  }
  .hero-img-area {
    right: -16%;
  }
  .hero-area .wel-shape-2 {
    right: -3%;
    top: 27%;
  }
  .hero-area .wel-shape-2 img {
    width: 55%;
  }
  .hero-area .wel-shape-4 {
    left: -1%;
    top: 20%;
  }
  .hero-area {
    height: 750px;
  }
  .hero-area .shape-1 {
    display: none;
  }
  .hero-area .shape-1 img {
    width: 30%;
  }
  .single-cate-card.relative {
    margin-bottom: 30px;
  }
  .about-us-area {
    padding-top: 110px;
  }
  .wel-shape-3 {
    display: none;
  }
  .section-title-area h2 {
    font-size: 32px;
  }
  .section-title-area h6 {
    font-size: 18px;
  }
  .fav-icon img {
    width: 65%;
  }
  .fav-course-area-3 .tab-btn-img-text .course-title-4 {
    font-size: 16px;
  }
  .fav-course-area-3 .tab-btn-img-text .course-nu {
    font-size: 12px;
  }
  .course-tab-heading li {
    margin-bottom: 15px;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text h2 {
    font-size: 36px;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text p {
    font-size: 14px;
    line-height: 28px;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text .course-about-meta li {
    font-size: 17px;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text {
    margin-right: 0;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text {
    flex: 0 0 60%;
    width: 60%;
    margin-right: 0;
  }
  .about-us-area .about-us-title-img .about-img {
    margin-top: 150px;
    position: relative;
  }
  .about-us-area .about-us-title-img .about-img .about-img-2 {
    right: 4%;
    top: -98px;
  }
  .team-area .team-block .single-team-card .team-img .member-info h3 {
    font-size: 22px;
  }
  .learn-skill-area .skill-shape-2 {
    right: 44%;
    top: 29%;
  }
  .skill-shape-2 img {
    width: 75%;
  }
  .learn-skill-area .skill-content-text h2 {
    font-size: 42px;
  }
  .single-counter-card .counter-info {
    padding: 50px 30px;
  }
  .learn-skill-area .skill-bg-img {
    transform: translateY(28%);
  }
  .top-categories-area-4 .cate-single-card-4 .cate-content-text-4 .cate-icon-4 {
    margin-right: 10px;
  }
  .heading-title-4 h6 {
    font-size: 18px;
  }
  .heading-title-4 h3 {
    font-size: 40px;
  }
  .featured-course-4-area .course-card-4-area .course-desc-4 {
    font-size: 14px;
    line-height: 25px;
  }
  .counter-upaarea-4 {
    padding-top: 140px;
    padding-bottom: 140px;
  }
  .featured-course-4-area .course-card-4-area h2 {
    font-size: 20px;
  }
  .top-categories-area-4 .cate-single-card-4 .cate-content-text-4 .cate-icon-4 i {
    font-size: 21px;
  }
  .top-categories-area-4 .cate-single-card-4 .cate-content-text-4 h3 {
    font-size: 18px;
  }
  .learn-skill-area {
    padding-bottom: 120px;
  }
  .team-area .team-block .single-team-card .team-img .member-info p {
    margin-top: 5px;
    font-size: 12px;
  }
  .about-us-area .about-shape-1 {
    top: 1%;
  }
  .single-counter-card .counter-info h2 {
    font-size: 51px;
    line-height: 50px;
  }
  .single-counter-card {
    margin-bottom: 30px;
  }
  .blog-area {
    padding-top: 110px;
    padding-bottom: 110px;
  }
  .single-blog-artical {
    margin-bottom: 30px;
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-icon {
    flex: 0 0 90px;
    height: 90px;
    width: 90px;
    line-height: 90px;
    margin-right: 20px;
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-icon span {
    font-size: 30px;
    line-height: 90px;
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-text h2 {
    font-size: 42px;
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-text p {
    font-size: 12px;
  }
  .counter-shape-2 img {
    width: 60%;
  }
  .counter-upaarea-2 .counter-shape-2 {
    top: 6%;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::before {
    right: 0;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::after {
    left: 0;
  }
  .care-program-area .single-care-card {
    padding: 50px 20px;
  }
  .certificate-area .gra-shape-3 {
    bottom: 13%;
    left: 26%;
    z-index: 9;
  }
  .client-area-home-2 .client-active-2 .client-info-meta-home-2 p {
    font-size: 14px;
    line-height: 24px;
  }
  .upcoming-event-area .single-event-card .event-card-content h4 {
    font-size: 22px;
  }
  .client-area-home-2 .client-active-2 {
    padding: 20px 50px;
    padding-bottom: 30px;
  }
  .faq-area .faq-content-area .accordion-button {
    font-size: 17px;
  }
  .footer-wrap .footer-content .quick-links-nav ul li a {
    font-size: 14px;
    line-height: 25px;
  }
  .cate-btn {
    font-size: 13px;
  }
  .faq-area .faq-content-area .accordion-item .accordion-body p {
    font-size: 14px;
    line-height: 25px;
  }
  .knowledge-cta-area-2 .knowledge-cta-content .skill-program-card .skill-program-text h4 {
    font-size: 22px;
  }
  .footer-wrap .footer-content .footer-desc {
    font-size: 18px;
  }
  .knowledge-cta-area-2 .knowledge-per-img {
    left: -13%;
  }
  .knowledge-cta-area-2 .knowledge-cta-content h2 {
    font-size: 40px;
  }
  .counter-area-home-2 .single-counter-2-card .counter-2-icon {
    flex: 0 0 110px;
    height: 110px;
    width: 110px;
    line-height: 110px;
  }
  .upcoming-event-area .single-event-card:hover {
    margin-left: 38px;
  }
  .upcoming-event-area .up-shape-1 {
    left: 0;
  }
  .counter-area-home-2 .single-counter-2-card .counter-2-icon span {
    font-size: 36px;
    line-height: 110px;
  }
  .client-area-home-2 .client-image-home-2::after {
    left: -49%;
  }
  .knowledge-cta-area-2 .knowledge-image::after {
    left: 11%;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-conetnt-card-2 h3 {
    font-size: 23px;
  }
  .footer-wrap .footer-content .quick-contact-nav ul li p {
    font-size: 13px;
  }
  .hero-content-text {
    margin-top: 0;
  }
  .header-area.four .navbar-nav li > a {
    padding: 0 1.2rem;
  }
  .footer-wrap .footer-content .footer-contact-info ul li {
    margin-bottom: 15px;
  }
  .hero-area-2 .hero-2-shape-1 {
    display: none;
  }
  .hero-area-4 .hero-content-text h2 {
    font-size: 56px;
  }
  .about-us-area .about-us-title-img h2 {
    font-size: 40px;
  }
  .about-us-area .about-us-title-img .img-badge-text p {
    font-size: 12px;
  }
  .about-us-area .about-us-title-img .img-badge-text p a {
    font-size: 15px;
  }
  .about-us-area .about-us-title-img .about-img .about-img-1 {
    width: 80%;
  }
  .about-us-area .about-desc-info-area p {
    font-size: 15px;
    line-height: 27px;
  }
  .about-us-area .about-desc-info-area .about-content-card-area .about-single-card p {
    font-size: 13px;
    line-height: 22px;
  }
  .about-us-area .about-shape-3 {
    bottom: -39px;
  }
  .about-us-area .about-shape-3 img {
    width: 85%;
  }
  .about-us-area .about-desc-info-area h6 {
    font-size: 19px;
  }
  .popular-categories-slider-area .cate-slider-area {
    transform: translateY(-40%);
  }
  .knowledge-cta-area {
    padding-top: 120px;
    padding-bottom: 120px;
  }
  .team-area .team-block .single-team-card .contact-social-icon .conatct-icon- ul {
    padding-bottom: 30px;
  }
  .client-area .clinet-heading-review-area .clinet-heading p {
    font-size: 14px;
    line-height: 26px;
  }
  .client-area .clinet-heading-review-area .clinet-heading {
    padding-right: 40px;
  }
  .client-area {
    padding-bottom: 120px;
  }
  .learn-skill-area .skill-content-text p {
    font-size: 15px;
    line-height: 27px;
  }
  .learn-skill-area .skill-content-text .skill-program-card .skill-program-text h4 {
    font-size: 20px;
  }
  .popular-cate-2-area.pricing-page {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .popular-cate-2-area.pricing-page .cate-2-shape {
    display: none;
  }
  .breadcrumb-area-2 {
    height: 400px;
  }
  .single-photograph .photo-title-area h2 {
    font-size: 56px;
    line-height: 80px;
  }
  .top-header.three .top-social-conatct-3 .header-3-bread li {
    font-size: 13px;
  }
  .top-header.three .contact-info a .contact-desc-3 {
    font-size: 13px;
  }
  .login-btn-3 {
    padding: 4px 10px;
    font-size: 12px;
    margin-left: 12px;
  }
  .header-area.three {
    padding: 20px 0;
    padding-top: 15px;
  }
  .top-serach-bar-four .form-control {
    height: 34px;
  }
  .carrer-life-area .carrer-content-info-3 h2 {
    font-size: 43px;
  }
  .carrer-life-area .carrer-content-info-3 .carrer-fexi-info-area .carrer-fexi-card h5 {
    font-size: 17px;
  }
}
@media (min-width: 320px) and (max-width: 991px) {
  .top-header.home-2 .navbar-brand img {
    width: 110px;
  }
  .top-header.three {
    height: 50px;
  }
  .contact-info {
    margin-bottom: 5px;
    margin-top: 5px;
  }
  .contact-info-3 {
    margin-top: 10px;
  }
  .cart-search-area.three .cart-badge {
    bottom: 17px;
    left: 28px;
  }
  .hero-area .hero-content-text {
    margin-top: 100px;
  }
  .header-area.three .navbar .navbar-brand {
    padding-right: 20px;
  }
  .header-area.three .cart-search-area.three .search-home-2.three {
    margin-right: 4px;
    padding-right: 10px;
  }
  .header-area.three {
    padding-bottom: 9px;
  }
  .single-photograph {
    height: 550px;
  }
  .single-photograph .photo-title-area {
    top: 63%;
  }
  .photography-nav-wrapper {
    bottom: -14px;
  }
  .photography-nav li {
    width: 40px;
    height: 40px;
  }
  .photography-nav li.tns-nav-active {
    border: 2px solid #6956F9;
  }
  .header-area.three.sticky .cart-search-area.three {
    margin-left: 0 !important;
    margin-top: 25px;
    display: none !important;
  }
  .blog-card-home-2 .blog-conetnt-card-2 h3 {
    font-size: 16px;
  }
  .blog-card-home-2 .blog-conetnt-card-2 .blog-header p {
    font-size: 12px;
    margin: 0 15px;
  }
  .blog-card-home-2 .blog-conetnt-card-2.home-3 {
    padding: 20px;
  }
  .blog-area-3 .blog-shape-2 {
    display: none;
  }
  .blog-card-home-2.home-3 {
    margin-top: 50px;
  }
  .tuition-fees-area {
    padding-top: 100px;
  }
  .header-area.three.sticky {
    padding-top: 0;
  }
  .cart-btn-area {
    margin-top: 15px;
  }
  .cart-search-area.three {
    margin-left: 0 !important;
  }
  .cart-search-area.three .cart-btn-area {
    padding: 0 15px;
    padding-bottom: 0;
  }
  .top-header.three .top-social-conatct-3 {
    flex-wrap: wrap;
  }
  .header-area.home-2 .navbar-toggler {
    color: #fff;
    background-color: #F8941F;
  }
  .header-area.home-2 .navbar-nav li > a {
    color: #808287;
  }
  .header-area.home-2 .cart-btn-area {
    top: 0;
    margin-top: 0;
  }
  .vw-search-area {
    padding: 50px 15px;
  }
  .body-overlay {
    z-index: 101010;
  }
  .header-area.home-2 .search-icon {
    margin-right: 9px;
    padding-right: 21px;
  }
  .mobile-menu {
    display: inline-block;
  }
  .hero-area-2 {
    height: 715px;
  }
  .hero-area-2 .hero-content-text {
    margin-top: none;
  }
  .hero-area-2 .hero-content-text {
    transform: none;
  }
  .hero-area-2 .hero-content-text {
    transform: translateY(35%);
  }
  .hero-area-2 .wel-img-2 {
    width: 72%;
  }
  .hero-area-2 .hero-2-shape-2 {
    bottom: -8%;
  }
  .hero-area-2 .bag-shape {
    display: none;
  }
  .hero-area-2 .hero-content-text h2 {
    font-size: 46px;
    line-height: 58px;
  }
  .hero-area-2 .hero-2-shape-1 {
    display: none;
  }
  .hero-area-2 .hero-2-shape-3 img {
    width: 40%;
  }
  .hero-area-2 .hero-2-shape-3 {
    top: 15%;
    right: -44%;
  }
  .certificate-area .gra-shape-2 {
    top: 3%;
    left: -21%;
  }
  .certificate-area .gra-shape-1 {
    top: 8%;
    left: 6%;
  }
  .certificate-area .gra-shape-1 img {
    width: 50%;
  }
  .popular-cate-2-area .cate-price-card .cate-price-body h6 {
    font-size: 21px;
  }
  .popular-cate-2-area .cate-price-card .cate-price-body ul li {
    font-size: 15px;
  }
  .popular-cate-2-area .cate-price-card .cate-price-body h2 {
    font-size: 35px;
  }
  .top-header.home-2 .header-top-home-2 {
    padding: 15px;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .contact-info-icon span {
    font-size: 16px;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .contact-info-icon {
    margin-right: 10px;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text h6 {
    font-size: 10px;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text p {
    font-size: 10px;
  }
  .header-2-contact-area {
    flex-wrap: wrap;
  }
  .desk-logo {
    display: none;
  }
  .header-2-contact-info {
    margin-bottom: 0;
  }
  .header-2-contact-info:last-child {
    margin-bottom: 0;
  }
  .header-area.home-2 {
    top: 55px;
  }
  .header-2-contact-info {
    flex: 0 0 30%;
    width: 30%;
  }
  .header-top-home-2 {
    background-color: transparent !important;
  }
  .hero-content-text h2 {
    font-size: 31px;
    line-height: 46px;
  }
  .hero-content-text p {
    line-height: 26px;
    font-size: 15px;
  }
  .hero-img-area {
    right: 0;
  }
  .hero-content-text {
    transform: translateY(-50%);
  }
  .hero-area .shape-1 img {
    width: 33%;
  }
  .hero-area .shape-1 {
    top: 19%;
  }
  .wel-shape-2 img {
    width: 30px;
  }
  .hero-area .shape-1 img {
    display: none;
  }
  .wel-shape-2 img {
    width: 70%;
  }
  .hero-area .wel-shape-2 {
    right: 3%;
    top: 20%;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text h2 {
    font-size: 28px;
  }
  .wel-shape-3 img {
    width: 50%;
  }
  .hero-content-text {
    margin-top: 0;
  }
  .hero-area .wel-shape-3 {
    right: -15%;
    top: 24%;
    z-index: 1010101;
  }
  .wel-shape-5 img {
    width: 45%;
  }
  .single-photograph .photo-title-area {
    width: 100%;
  }
  .hero-area .wel-shape-5 {
    left: -7%;
  }
  .single-photograph .photo-title-area h2 {
    font-size: 36px;
    line-height: 57px;
  }
  .single-photograph .photo-title-area h6 {
    font-size: 20px;
  }
  .carrer-life-area .carrer-image-2 {
    display: none;
  }
  .carrer-life-area .carrer-content-info-3 h5 {
    font-size: 18px;
  }
  .carrer-life-area .carrer-content-info-3 .carrer-fexi-info-area .carrer-fexi-icon i {
    font-size: 36px;
  }
  .carrer-life-area .carrer-content-info-3 h2 {
    font-size: 30px;
  }
  .carrer-shape-3 {
    display: none;
  }
  .carrer-content-info-3.wow.fadeInRight {
    margin-top: 50px;
  }
  .carrer-life-area .carrer-3-desc {
    width: 100%;
  }
  .hero-area .wel-shape-4 {
    position: absolute;
    top: 5%;
    left: 5%;
  }
  .top-serach-bar .form-control {
    height: 35px;
  }
  .contact-info a span {
    font-size: 12px;
  }
  .contact-desc {
    margin-left: 4px;
    font-size: 11px;
  }
  .top-header .top-social-icon .header-icon li span {
    font-size: 12px;
  }
  .top-header .top-social-icon .header-icon li a span {
    font-size: 13px;
    margin-right: 6px;
  }
  .login-btn {
    height: 25px;
    width: 73px;
    font-size: 11px;
  }
  .hero-area-4 .hero-content-text {
    transform: translateY(15%);
  }
  .student-tab-area .nav-pills .nav-link {
    font-size: 17px;
  }
  .cate-single-slier {
    margin-bottom: 30px;
  }
  .student-tab-area .nav-pills .nav-link.active::after,
  .student-tab-area .nav-pills .show > .nav-link::after {
    bottom: -7px;
  }
  .student-view-area {
    padding-bottom: 70px;
  }
  .how-it-work-area-3 .play-btn.three {
    height: 70px;
    width: 70px;
  }
  .how-it-work-area-3 .play-btn.three .video_player_btn a {
    font-size: 21px;
    line-height: 76px;
  }
  .how-it-work-area-3 .play-btn.three::after {
    height: 55px;
    width: 55px;
  }
  .student-tab-area .nav-pills .nav-link {
    margin-right: 25px;
    padding: 0;
  }
  .fav-course-area-3 {
    padding: 100px 0;
  }
  .fav-course-area-3 .course-content-3-area .course-heading h2 {
    font-size: 32px;
    margin-bottom: 20px;
  }
  .fav-course-area-3 .course-content-3-area .course-content-info .course-du-card p {
    font-size: 11px;
    line-height: 18px;
  }
  .our-program-area .pro-btn-3-area {
    margin-bottom: 60px;
  }
  .our-program-area .progra-img-area .course-title {
    font-size: 30px;
  }
  .our-program-area .pro-btn-3-area {
    text-align: left;
  }
  .course-image {
    margin-top: 50px;
  }
  .tut-shape-1 {
    display: none;
  }
  .breadcrumb-area-2 {
    height: 450px;
  }
  .breadcrumb-area-2 .breadcrumb-conetnt .bread-list li {
    font-size: 13px;
  }
  .breadcrumb-area-2 .breadcrumb-conetnt h4 {
    font-size: 24px;
    margin-top: 18px;
  }
  .course-details-area .course-title {
    flex-wrap: wrap;
  }
  .course-details-area {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .course-details-area .course-info-card-area .course-info-card {
    flex: 0 0 50%;
    margin-bottom: 25px;
  }
  .course-details-area .other-ins-area .inst-member-info .inst-info h4 {
    font-size: 19px;
  }
  .course-details-area .other-ins-area .inst-member-info .inst-img {
    flex: 0 0 110px;
    width: 110px;
    margin-right: 20px;
  }
  .sidebar-content-single-area .side-bar-video-area .enroll-course-content .course-info-list li {
    font-size: 16px;
  }
  .course-details-area .other-ins-area .inst-member-info .inst-info p {
    font-size: 12px;
  }
  .course-details-area .course-details-tab-content p {
    font-size: 15px;
  }
  .grid-filter-btn {
    flex-wrap: wrap;
  }
  .breadcrumb-area .breadcrumb-conetnt {
    margin-top: 0;
  }
  .about-us-area.single-page .about-single-image {
    margin-bottom: 40px;
  }
  .breadcrumb-area {
    height: 540px;
  }
  .breadcrumb-area .grid-filter-btn-list {
    margin-bottom: 15px;
  }
  .breadcrumb-area .grid-filter-btn-list {
    flex: 0 0 100%;
  }
  button.filter-btn {
    margin-top: 15px;
  }
  .breadcrumb-area .grid-filter-search {
    padding: 15px;
  }
  .grid-filter-search {
    flex-wrap: wrap;
  }
  .course-details-area .course-bg-img {
    margin-top: 0;
  }
  .course-details-area .course-detials-tab .nav-pills .nav-link {
    font-size: 20px;
  }
  .course-details-area .course-info-card-area .course-info-card h4 {
    font-size: 20px;
  }
  .course-details-area .course-info-card-area {
    flex-wrap: wrap;
  }
  .course-details-area .course-title h2 {
    font-size: 32px;
    margin-bottom: 25px;
  }
  .breadcrumb-area-2 .breadcrumb-conetnt {
    margin-top: 30px;
  }
  .popular-cate-2-area.pricing-page {
    padding-top: 100px;
    padding-bottom: 150px;
  }
  .fav-course-area-3 .course-content-3-area .course-content-info .course-du-card h3 {
    font-size: 15px;
  }
  .tuition-fees-area .tutu-fee-acc-area .accordion-button {
    font-size: 16px;
  }
  .tutu-fee-img {
    margin-bottom: 50px;
  }
  .tuition-fees-area .tutu-fee-acc-area .tuition-fee-ac-body p {
    font-size: 14px;
  }
  .tuition-fee-ac-body ul {
    padding-left: 0;
  }
  .client-area-3 {
    padding-top: 90px;
  }
  .client-area-3 .client-shape-3 {
    display: none;
  }
  .blog-area-3 {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .client-area-3 .single-client-3 .client-title-area p {
    font-size: 15px;
    line-height: 28px;
  }
  .tuition-fees-area .tutu-fee-acc-area .tuition-fee-ac-body ul .fee-list {
    font-size: 16px;
  }
  .client-area-3 .single-client-3 {
    padding-left: 0;
    margin-top: 100px;
  }
  .fav-course-area-3 .course-content-3-area .course-content-info .course-du-card {
    padding: 15px;
  }
  .fav-course-area-3 .tab-btn-img-text p {
    font-size: 12px;
  }
  .fav-course-area-3 .course-tab-heading .nav-link {
    margin-bottom: 15px;
  }
  .how-it-work-area-3 {
    padding-top: 100px;
  }
  .carrer-life-area {
    padding: 100px 0;
  }
  .student-tab-area .student-tab-heading {
    margin-bottom: 20px;
  }
  .student-tab-heading li {
    margin-bottom: 30px;
  }
  .cart-btn-area {
    padding: 0 15px;
    padding-bottom: 15px;
  }
  .cart-badge {
    bottom: 32px;
    left: 28px;
  }
  .top-header {
    height: 80px;
  }
  .top-dropdown {
    flex: 0 0 30%;
  }
  .top-serach-bar {
    flex: 0 0 70%;
    padding-right: 10px;
  }
  .top-serach-bar .form-control {
    width: 100%;
  }
  .top-serach-bar .search-btn {
    right: 20px;
  }
  .top-social-conatct {
    justify-content: space-between;
  }
  .header-area .navbar-collapse {
    background-color: #fff;
  }
  .menu-area- {
    padding: 0 15px;
  }
  .header-area {
    top: 70px;
  }
  .top-header.three .top-social-conatct-3 {
    display: none !important;
  }
  .top-header {
    padding: 8px 0;
    padding-top: 4px;
  }
  .top-serach-bar .form-control {
    height: 30px;
  }
  .header-area.three {
    top: 50px;
  }
  .cart-search-area.three.desktop {
    display: none !important;
  }
  .header-area.three.sticky {
    top: 0;
  }
  .header-area.three {
    padding-top: 0;
  }
  .header-area.three .cart-btn-area {
    margin-top: 0;
  }
  .menu-area- {
    padding: 10px 15px;
  }
  .shop-cart-area {
    padding-top: 100px;
  }
  .cate-btn {
    font-size: 14px;
  }
  .product-info-details-area .qty-pro-area {
    flex-wrap: wrap;
  }
  .product-info-details-area .cart-btn {
    padding: 10px;
    width: 115px;
    margin-left: 20px !important;
  }
  .ms-5 {
    margin-left: 0 !important;
  }
  .product-info-details-area .qty-pro-area p {
    margin-bottom: 15px;
    margin-top: 15px;
  }
  .simi-title {
    font-size: 38px;
  }
  .shop-area.shop-single {
    padding-top: 100px;
    padding-bottom: 70px;
  }
  .dropdown-search-menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  .section-title-area h2 {
    font-size: 26px;
    line-height: 1.3;
  }
  .how-we-work-area .how-it-work-content-3.single-page .play-btn {
    height: 90px;
    width: 90px;
  }
  .how-we-work-area {
    padding-top: 10px;
    padding-bottom: 100px;
  }
  .contact-map .conatct-info-meta .contact-single-info-card {
    padding: 30px;
  }
  .contact-us-area.page-2 {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .event-card-single-inner .add-time-info p {
    font-size: 12px;
    margin-right: 9px;
  }
  .event-details-tab-content .event-details-list li {
    font-size: 14px;
  }
  .event-list-inner-page-area {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .contact-map .conatct-info-meta {
    position: relative;
    top: 0;
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 100px;
  }
  .contact-map .conatct-info-meta {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  .how-we-work-area .how-we-content h2 {
    font-size: 30px;
  }
  .how-it-work-content-3.single-page {
    margin-top: 50px;
  }
  .about-us-area .about-us-title-img h2 {
    font-size: 27px;
    margin-bottom: 25px;
  }
  .about-us-area .about-us-title-img .img-badge-text p {
    font-size: 14px;
  }
  .about-us-area .about-us-title-img .img-badge-text p a {
    font-size: 18px;
  }
  .single-cate-card {
    margin-bottom: 30px;
  }
  .about-us-area .about-shape-1 {
    top: 0;
  }
  .about-us-area .img-badge {
    flex: 0 0 40%;
    width: 40%;
  }
  .img-badge-text {
    margin-top: 30px;
  }
  .about-us-area .about-us-title-img .about-img {
    margin-top: 30px;
  }
  .about-us-area .about-us-title-img .about-img .about-img-2 {
    position: relative;
    top: 0;
    right: 0;
    width: 100%;
  }
  .about-us-area .about-us-title-img .about-img .about-img-1 {
    margin-bottom: 15px;
  }
  .about-desc-info-area {
    margin-top: 35px;
  }
  .text-slider-section {
    margin-bottom: 80px;
  }
  .popular-categories-slider-area .cate-slider-area .owl-dots {
    margin-top: 0px;
  }
  .partner-area {
    padding-top: 70px;
    padding-bottom: 100px;
  }
  .popular-categories-slider-area .cate-slider-area {
    transform: translateY(-42%);
  }
  .about-us-area .about-shape-3 img {
    width: 60%;
  }
  .about-us-area .about-shape-3 {
    right: 0;
  }
  .partner-area .partner-area-content {
    grid-template-columns: 1fr 1fr 1fr;
  }
  .text-slider-section .text-slider-box {
    animation: aspro-scroller-reverse 20s linear infinite;
  }
  .text-slider-section .text-slider-box .slide-box h2 {
    font-size: 56px;
  }
  .counter-upaarea-2::after {
    height: 100%;
    width: 100px;
    right: -40%;
  }
  .counter-upaarea-2 .counter-shape-2 img {
    width: 40%;
  }
  .counter-upaarea-2 .counter-shape-2 {
    display: none;
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-icon {
    flex: 0 0 100px;
    height: 100px;
    width: 100px;
    line-height: 100px;
  }
  .counter-upaarea-2 .single-counter-2-card .counter-2-icon span {
    font-size: 36px;
    line-height: 100px;
  }
  .counter-upaarea-2 .single-counter-2-card {
    margin-bottom: 30px;
  }
  .client-area {
    padding-bottom: 90px;
  }
  .counter-upaarea-2 {
    padding-bottom: 90px;
  }
  .section-title-area h6 {
    font-size: 18px;
    margin-bottom: 15px;
  }
  .single-fav-course {
    margin-bottom: 30px;
  }
  .favorite-course-area {
    padding-bottom: 70px;
    padding-top: 100px;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-img {
    flex: 0 0 100%;
    margin-right: 0;
    margin-bottom: 50px;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text {
    flex: 0 0 100%;
    width: 100%;
    margin-right: 0;
  }
  .client-area .clinet-heading-review-area .clinet-heading {
    flex: 0 0 100%;
    width: 100%;
    padding-right: 0;
  }
  .client-area .clinet-heading-review-area .clinet-slider-area {
    flex: 0 0 100%;
    width: 100%;
  }
  .client-area .clinet-heading-review-area {
    padding-left: 7%;
    flex-wrap: wrap;
  }
  .client-area .swiper {
    padding: 60px 0;
    padding-right: 0;
    margin-top: 40px;
  }
  .learn-skill-area .skill-content-text h2 {
    font-size: 28px;
  }
  .learn-skill-area .skill-content-text .skill-program-card:first-child {
    margin-right: 0;
  }
  .learn-skill-area .skill-content-text p {
    font-size: 15px;
    line-height: 27px;
  }
  .learn-skill-area .skill-content-text .skill-program-card .skill-program-text h4 {
    font-size: 20px;
  }
  .skill-program-card {
    flex: 0 0 100%;
    width: 100%;
  }
  .skill-bg-img {
    margin-top: 30px;
  }
  .skill-program-info {
    flex-wrap: wrap;
  }
  .single-counter-card {
    text-align: center;
    margin-bottom: 30px;
  }
  .skill-program-card {
    margin-bottom: 30px;
  }
  .skill-program-card:last-child {
    margin-bottom: 0;
  }
  .learn-skill-area .skill-bg-img {
    transform: translateY(13%);
  }
  .blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content h3 {
    font-size: 20px;
  }
  .blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content p {
    font-size: 13px;
    line-height: 25px;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text::before {
    bottom: -30%;
    width: 50%;
    height: 135%;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text::after {
    bottom: -30%;
    width: 50%;
    height: 135%;
  }
  .blog-area .single-blog-artical .single-blog-card .date-badge {
    top: -61%;
  }
  .blog-area .single-blog-artical .single-blog-card .b-shape-img .date-badge {
    top: -10%;
  }
  .blog-area {
    padding-top: 70px;
    padding-bottom: 70px;
  }
  .single-blog-artical {
    margin-bottom: 50px;
  }
  .quick-links-nav {
    margin-top: 40px;
  }
  .quick-contact-nav {
    margin-top: 40px;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text {
    padding: 30px 10px;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text h3 {
    font-size: 20px;
  }
  .hero-content-text h6 {
    font-size: 19px;
  }
  .team-area .team-block .single-team-card .border-s-5 {
    bottom: -11%;
    left: 15%;
    height: 79%;
    transform: rotate(-51deg);
  }
  .team-area .team-block .single-team-card .border-s-4 {
    bottom: -11%;
    right: 15%;
    height: 79%;
    transform: rotate(51deg);
  }
  .team-area .team-block .single-team-card .border-s-2 {
    height: 61%;
  }
  .team-area .team-block .single-team-card .border-s-3 {
    height: 61%;
  }
  .log-regi-area {
    padding-top: 100px;
    padding-bottom: 50px;
  }
  .log-regi-area .log-area {
    margin-bottom: 50px;
  }
  .log-regi-area .log-area .lable-text {
    font-size: 16px;
  }
  .log-regi-area .log-area .form-control {
    font-size: 16px;
  }
  .rem-forgot-btn {
    flex-wrap: wrap;
  }
  .log-regi-area .log-area h2 {
    font-size: 32px;
  }
  .log-regi-area .log-area {
    padding: 40px 30px;
  }
  .log-regi-area .log-area .forgot-pass a {
    margin-top: 15px;
    display: block;
  }
  .team-img img {
    width: 100%;
  }
  .auth-btn {
    padding: 10px 15px;
    font-size: 16px;
  }
  .team-area .team-block .single-team-card {
    margin-bottom: 40px;
    overflow: hidden;
  }
  .log-regi-area .log-area .auth-desc {
    font-size: 14px;
    margin-top: 35px;
    line-height: 1.7;
  }
  .team-area {
    padding-bottom: 70px;
    padding-top: 100px;
  }
  .contact-info {
    justify-content: space-between;
  }
  .hero-area {
    height: 970px;
  }
  .wel-shape-7 img {
    width: 45%;
  }
  .hero-area .wel-shape-7 {
    right: -3%;
    top: 18%;
  }
  .dropdown-search-menu {
    margin-top: 15px;
  }
  .top-social-conatct {
    margin-top: 0;
  }
  .popular-cata-area {
    padding-top: 100px;
  }
  .about-us-area {
    padding-top: 100px;
  }
  .knowledge-cta-area {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .team-area .team-block .single-team-card .team-img::before {
    width: 44%;
    bottom: -39%;
    right: -17%;
  }
  .team-area .team-block .single-team-card .team-img::after {
    width: 44%;
    bottom: -39%;
    left: -17%;
  }
  .client-area {
    padding-top: 0;
  }
  .client-area .client-map-img {
    top: -4%;
  }
  .section-title-area h2 {
    font-size: 26px;
    line-height: 1.4;
  }
  .learn-skill-area .skill-shape-2 {
    display: none;
  }
  .about-us-area .img-badge-text {
    flex-wrap: wrap;
  }
  .img-badge-text- {
    margin-top: 20px;
  }
  .fav-shape-1 {
    display: none;
  }
  .header-area.sticky .menu-area- {
    padding: 10px 20px;
    padding-top: 3px;
  }
  .about-us-area .about-shape-2 {
    display: none;
  }
  .about-img img {
    width: 100%;
  }
  .single-counter-card .counter-info h2 {
    font-size: 64px;
    line-height: 80px;
  }
  .blog-shape-1 {
    display: none;
  }
  .care-program-area {
    padding-top: 100px;
    padding-bottom: 70px;
  }
  .care-program-area .single-care-card {
    margin-bottom: 30px;
    padding: 50px 30px;
  }
  .padding-140-0 {
    padding-top: 100px;
  }
  .client-area-3 {
    padding-bottom: 0;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::before {
    right: 5%;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::after {
    left: 5%;
  }
  .popular-cate-2-area .cate-price-card {
    margin-bottom: 30px;
  }
  .cate-2-shape {
    width: 33%;
    right: 0;
  }
  .certificate-area .gra-shape-4 {
    display: none;
  }
  .certificate-area .certi-title-area h2 {
    font-size: 27px;
  }
  .certificate-area .certi-title-area {
    padding-left: 0;
    margin-top: 195px;
  }
  .certificate-area .certi-single-card .certi-border .certi-icon {
    flex: 0 0 60px;
    height: 60px;
    width: 60px;
    line-height: 60px;
  }
  .certificate-area .gra-shape-3 {
    display: none;
  }
  .certificate-area .gra-cert-image-area .play-btn {
    height: 90px;
    width: 90px;
  }
  .certificate-area .gra-cert-image-area .play-btn .video_player_btn {
    line-height: 90px;
  }
  .certificate-area .gra-cert-image-area .play-btn::after {
    height: 75px;
    width: 75px;
  }
  .play-btn .video_player_btn a {
    font-size: 32px;
    line-height: 90px;
  }
  .certificate-area .gra-cert-image-area .play-btn {
    right: 1%;
    bottom: -33%;
  }
  .certificate-area .gra-cert-image-area .certi-img-2 {
    top: 83%;
  }
  .certificate-area .certi-single-card .certi-card-info p {
    font-size: 14px;
    line-height: 24px;
  }
  .certificate-area .certi-single-card .certi-card-info h4 {
    font-size: 20px;
  }
  .counter-area-home-2 .single-counter-2-card .counter-2-icon {
    flex: 0 0 90px;
    height: 90px;
    width: 90px;
    line-height: 90px;
  }
  .counter-area-home-2 .counter-2-text h2 {
    font-size: 44px;
  }
  .upcoming-event-area .single-event-card:hover {
    margin-left: 0;
  }
  .counter-area-home-2 .single-counter-2-card .counter-2-icon span {
    font-size: 32px;
    line-height: 90px;
  }
  .counter-area-home-2 .single-counter-2-card {
    margin-bottom: 30px;
  }
  .counter-area-home-2 {
    padding-bottom: 30px;
  }
  .counter-area-home-2 {
    transform: translateY(110px);
  }
  .portfolio-area .g-shape-1 {
    top: 0;
  }
  .portfolio-area .g-shape-1 img {
    width: 80%;
  }
  .portfolio-area .g-shape-3 {
    left: 62%;
  }
  .upcoming-event-area .up-shape-2 {
    top: 6%;
  }
  .up-shape-1 {
    display: none;
  }
  .event-content-text {
    margin-top: 95px;
  }
  .upcoming-event-area .single-event-card .event-price {
    margin-right: 20px;
  }
  .upcoming-event-area .single-event-card .event-card-content p {
    font-size: 12px;
  }
  .upcoming-event-area .single-event-card .event-card-content h4 {
    font-size: 18px;
  }
  .upcoming-event-area .single-event-card .event-price {
    height: 80px;
    width: 80px;
    flex: 0 0 80px;
  }
  .client-area-home-2 .client-image-home-2::after {
    left: -48%;
  }
  .upcoming-event-area .single-event-card .event-price h5 {
    font-size: 20px;
    line-height: 80px;
  }
  .upcoming-event-area .up-shape-3 {
    bottom: 4%;
  }
  .client-area-home-2 .client-image-home-2 {
    margin-bottom: 50px;
  }
  .client-area-home-2 .client-active-2 .client-info-meta-home-2 p {
    font-size: 14px;
    line-height: 25px;
  }
  .client-area-home-2 .client-active-2 {
    padding: 30px;
  }
  .faq-area .faq-shape-2 {
    top: 2%;
  }
  .faq-area .faq-shape-2 img {
    width: 70%;
  }
  .faq-area .faq-shape-1 {
    z-index: -1;
  }
  .add-card-alert-area {
    flex-wrap: wrap;
  }
  .add-card-alert-area p {
    margin-bottom: 20px;
  }
  .product-cart-area .table td,
  .product-cart-area .table th {
    font-size: 13px;
    padding: 1rem;
  }
  .coupon-cart-btn-area {
    margin-top: 50px;
  }
  .coupon-cart-btn-area .apply-coupon .coupon-form .form-control {
    height: 50px;
  }
  .coupon-cart-btn-area .apply-coupon {
    flex-wrap: wrap;
  }
  .coupon-cart-btn-area .apply-coupon .coupon-form .cop-form .btn-copon {
    font-size: 11px;
    width: 180px;
    padding: 0 9px;
  }
  .product-cart-area {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .coupon-cart-btn-area .update-btn {
    font-size: 16px;
    height: 50px;
    padding: 0 15px;
    margin-top: 30px;
    width: 100%;
  }
  .knowledge-cta-area-2 .knowledge-image img {
    height: 300px;
    width: 300px;
  }
  .knowledge-cta-area-2 .knowledge-image::after {
    left: 2%;
    border: 10px dashed #fff;
    z-index: 99;
    height: 300px;
    width: 300px;
  }
  .knowledge-cta-area-2 .knowledge-cta-content .skill-program-card .skill-program-text h4 {
    font-size: 20px;
  }
  .knowledge-cta-area-2 .knowledge-cta-content .skill-program-card .program-icon span {
    font-size: 40px;
  }
  .knowledge-cta-area-2 .knowledge-cta-content h2 {
    font-size: 27px;
    line-height: 1.5;
  }
  .knowledge-cta-content {
    margin-top: 100px;
    position: relative;
    z-index: 10;
  }
  .knowledge-cta-area-2 {
    padding-top: 140px;
  }
  .knowledge-cta-area-2 {
    padding-top: 150px;
    padding-bottom: 150px;
  }
  .knowledge-cta-area-2 .knowledge-per-img {
    left: 0;
    top: 36%;
  }
  .knowledge-cta-area-2 .knowledge-per-img img {
    width: 50%;
  }
  .blog-card-home-2 {
    margin-bottom: 40px;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-conetnt-card-2 h3 {
    font-size: 16px;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-conetnt-card-2 {
    padding: 20px 15px;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-conetnt-card-2 .blog-header p {
    font-size: 12px;
    margin: 0 10px;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-image-2 .date-badge {
    height: 70px;
    width: 70px;
    top: 10px;
    left: 10px;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-image-2 .date-badge h5 {
    font-size: 19px;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-image-2 .date-badge p {
    font-size: 13px;
  }
  .blog-area-home-2 .blog-card-home-2 {
    padding: 15px;
  }
  .blog-area-home-2 {
    padding-bottom: 60px;
    padding-top: 100px;
  }
  .faq-area .faq-content-area .accordion-item .accordion-body p {
    font-size: 14px;
    line-height: 25px;
  }
  .faq-area .faq-content-area .accordion-button {
    font-size: 16px;
  }
  .faq-area {
    padding: 100px 0;
    padding-bottom: 60px;
  }
  .portfolio-area {
    padding-top: 240px;
    padding-bottom: 100px;
  }
  .upcoming-event-area {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .header-area.home-2.sticky {
    background-color: #6956F9;
  }
  .top-serach-bar-four .form-control {
    height: 30px;
    width: 100%;
  }
  .top-header.four {
    height: 85px;
  }
  .top-header-4-content {
    flex-wrap: wrap;
  }
  .top-header-4-content .top-serach-bar-four {
    width: 100%;
  }
  .top-header-4-content .dropdown-search-menu {
    margin-top: 10px;
  }
  .top-header-4-content .dropdown-search-menu {
    flex: 0 0 100%;
  }
  .top-header.four .lan-btn {
    font-size: 13px;
  }
  .top-dropdown button i {
    font-size: 11px;
  }
  .hero-area .wel-shape-course-title {
    display: none;
  }
  .hero-area .wel-shape-4 {
    display: none;
  }
  .hero-area .wel-shape-2 {
    display: none;
  }
  .cart-login--area-4 {
    position: absolute;
    right: 5%;
    top: 11%;
  }
  .cart-login--area-4 .cart-btn-area-4 a {
    font-size: 13px;
  }
  .header-area.four.sticky {
    top: 0;
  }
  .cart-btn-area a {
    font-size: 16px;
  }
  .search-toggle-open.header-search .search-icon span {
    font-size: 15px;
  }
  .header-area.home-2 .cart-search-area {
    padding: 8px 20px;
  }
  .header-area.home-2 .cart-search-area .cart-btn-area {
    padding: 0 15px;
    padding-bottom: 0;
  }
  .header-area.home-2 .cart-search-area .cart-badge {
    bottom: 13px;
  }
  .header-area.four {
    top: 84px;
    padding: 0;
  }
  .hero-area-4 {
    height: 750px;
  }
  .hero-area-4 .hero-content-text h2 {
    font-size: 30px;
  }
  .heading-title-4 h3 {
    font-size: 28px;
  }
  .heading-title-4 h6 {
    font-size: 16px;
  }
  .top-categories-area-4 {
    padding-top: 100px;
    padding-bottom: 70px;
  }
  .heading-btn-4 a {
    font-size: 12px;
    padding: 5px 9px;
  }
  .featured-course-4-area .course-card-4-area h2 {
    font-size: 22px;
  }
  .counter-upaarea-4 {
    padding-top: 100px;
    padding-bottom: 70px;
  }
  .counter-upaarea-4 .single-counter-4-card {
    margin-bottom: 30px;
  }
  .counter-upaarea-4 .single-counter-4-card .counter-4-icon {
    flex: 0 0 100px;
    height: 100px;
    width: 100px;
    line-height: 100px;
  }
  .counter-upaarea-4 .single-counter-4-card .counter-4-icon span {
    font-size: 36px;
    line-height: 100px;
  }
  .counter-up-area .row > * {
    padding-right: 30px;
    padding-left: 30px;
  }
  .top-header.home-2 .header-top-home-2 {
    padding: 5px 0;
  }
  .cate-single-slier .cate-img img {
    width: 100%;
  }
  .blog-area-3 .blog-card-news-3 .news-img-3 {
    flex: 0 0 30%;
  }
  .news-content-3 {
    flex: 0 0 70%;
  }
  .blog-area-3 .blog-card-news-3 .news-content-3 .news-f-user-info-3 p {
    font-size: 12px;
    margin-right: 15px;
  }
  .blog-area-3 .blog-card-news-3 .news-content-3 h4 {
    font-size: 16px;
  }
  .blog-area-3 {
    padding-bottom: 60px;
  }
  .client-area-3 .client-nav li img {
    height: auto;
    width: 100%;
  }
  .searc-filed-cart-area .search-filter-box {
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }
  .form-label {
    margin-bottom: 5px;
    font-size: 10px;
  }
  .searc-filed-cart-area .search-filter-box .form-control {
    font-size: 11px;
    height: 30px;
  }
  .searc-filed-cart-area {
    padding: 15px;
    bottom: -229px;
    width: 91%;
  }
  .course-card-4-area.course-list {
    flex-wrap: wrap;
  }
  .course-card-4-area.course-list .course-img-4.single {
    flex: 0 0 100%;
  }
  .course-card-4-area.course-list .course-content-info-4.single {
    padding-left: 0;
    flex: 0 0 100%;
    margin-top: 30px;
  }
  .featured-course-4-area.single-page {
    padding-top: 100px;
  }
  .course-card-4-area.course-list .course-content-info-4.single h2 a {
    font-size: 21px;
  }
  .featured-course-4-area .course-card-4-area .course-desc-4 {
    font-size: 14px;
    line-height: 26px;
  }
  .footer-logo img {
    width: 170px;
  }
  .footer-wrap .footer-content .footer-desc {
    margin-top: 40px;
  }
  .footer-wrap .footer-content .footer-desc {
    font-size: 20px;
  }
}
@media (min-width: 360px) and (max-width: 570px) {
  .hero-content-text h2 {
    font-size: 35px;
    line-height: 48px;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text h6 {
    font-size: 11px;
  }
  .about-us-area .about-us-title-img h2 {
    font-size: 33px;
  }
  .team-area .team-block .single-team-card .team-img .member-info {
    padding-top: 10%;
    padding-bottom: 24%;
  }
  .team-area .team-block .single-team-card .contact-social-icon .share-icon .share--icon {
    bottom: 25%;
  }
  .hero-area .hero-content-text {
    margin-top: 15px;
  }
  .footer-logo img {
    width: 170px;
  }
}
@media (min-width: 480px) and (max-width: 575px) {
  .hero-content-text h2 {
    font-size: 46px;
    line-height: 58px;
  }
  .heading-btn-4 a {
    font-size: 13px;
    padding: 5px 15px;
  }
  .client-area-home-2 .client-image-home-2::after {
    left: -23%;
  }
  .upcoming-event-area .single-event-card .event-card-content h4 {
    font-size: 20px;
  }
  .certificate-area .gra-cert-image-area .play-btn {
    right: 9%;
    bottom: -13%;
  }
  .certificate-area .gra-shape-2 {
    left: -10%;
  }
  .hero-area-2 .hero-2-shape-3 {
    top: 15%;
    right: -29%;
  }
  .hero-area-4 .hero-content-text h2 {
    font-size: 36px;
  }
  .hero-area-2 .hero-content-text h2 {
    font-size: 55px;
    line-height: 67px;
  }
  .log-regi-area .log-area .forgot-pass a {
    margin-top: 0;
  }
  .hero-area-2 {
    height: 900px;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::before {
    right: 20%;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::after {
    left: 20%;
  }
  .section-title-area h2 {
    font-size: 40px;
  }
  .certificate-area .certi-title-area {
    padding-left: 0;
    margin-top: 280px;
  }
  .certificate-area .certi-title-area h2 {
    font-size: 40px;
  }
  .knowledge-cta-area-2 .knowledge-image::after {
    left: 17%;
  }
  .knowledge-cta-area-2 .knowledge-cta-content h2 {
    font-size: 40px;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-conetnt-card-2 h3 {
    font-size: 21px;
  }
  .contact-info {
    justify-content: flex-start;
  }
  .hero-area {
    height: 1100px;
  }
  .contact-info a span {
    font-size: 12px;
  }
  .hero-content-text {
    transform: translateY(-60%);
  }
  .hero-area .shape-1 {
    top: 15%;
  }
  .about-us-area .about-us-title-img h2 {
    font-size: 41px;
    margin-bottom: 30px;
  }
  .popular-categories-slider-area .cate-slider-area {
    transform: translateY(-37%);
  }
  .section-title-area h2 {
    font-size: 30px;
  }
  .team-img img {
    width: 100%;
  }
  .team-area {
    padding-bottom: 100px;
  }
  .team-area .team-block .single-team-card {
    margin-bottom: 40px;
  }
  .team-area .team-block .single-team-card .border-s-5 {
    bottom: -12%;
    left: 25%;
  }
  .team-area .team-block .single-team-card .border-s-4 {
    bottom: -12%;
    right: 25%;
  }
  .team-area .team-block .single-team-card .team-img::before {
    width: 156px;
    bottom: -151px;
    right: -23%;
  }
  .team-area .team-block .single-team-card .team-img::after {
    width: 156px;
    bottom: -151px;
    left: -22%;
  }
  .team-area .team-block .single-team-card .team-img .member-info {
    padding-top: 26px;
    padding-bottom: 112px;
  }
  .team-area .team-block .single-team-card .border-s-2 {
    height: 62%;
  }
  .team-area .team-block .single-team-card .border-s-3 {
    height: 62%;
  }
  .team-area .team-block .single-team-card .border-s-4 {
    bottom: -9%;
    right: 25%;
  }
  .team-area .team-block .single-team-card .border-s-5 {
    bottom: -9%;
    left: 25%;
  }
  .team-area .team-block .single-team-card .contact-social-icon .share-icon .share--icon {
    bottom: 26%;
  }
  .team-area .team-block .single-team-card .contact-social-icon .conatct-icon- ul {
    padding-bottom: 60px;
  }
  .section-title-area h2 {
    font-size: 36px;
  }
  .learn-skill-area .skill-content-text h2 {
    font-size: 39px;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text::before {
    bottom: -26%;
    width: 50%;
    height: 130%;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text::after {
    bottom: -26%;
    width: 50%;
    height: 130%;
  }
}
@media (min-width: 576px) and (max-width: 767px) {
  .hero-content-text h2 {
    font-size: 50px;
    line-height: 66px;
  }
  .counter-up-area .row > * {
    padding-right: 20px;
    padding-left: 20px;
  }
  .knowledge-cta-area-2 .knowledge-image::after {
    left: 21%;
  }
  .hero-area-4 .hero-content-text h2 {
    font-size: 40px;
  }
  .skill-program-info {
    flex-wrap: wrap;
  }
  .knowledge-cta-area-2 .knowledge-cta-content .skill-program-card:first-child {
    margin-right: 0;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-conetnt-card-2 h3 {
    font-size: 26px;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-conetnt-card-2 .blog-header p {
    font-size: 13px;
  }
  .contact-info {
    justify-content: flex-start;
  }
  .hero-area {
    height: 1250px;
  }
  .hero-area-4 .hero-content-text {
    transform: translateY(4%);
  }
  .client-area-3 .client-nav li img {
    width: 100%;
  }
  .product-tab-title .nav-link {
    font-size: 18px;
  }
  .hero-content-text {
    transform: translateY(-90%);
  }
  .section-title-area h2 {
    font-size: 36px;
  }
  .about-us-area .about-us-title-img h2 {
    font-size: 45px;
  }
  .popular-categories-slider-area .cate-slider-area {
    transform: translateY(-37%);
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text h2 {
    font-size: 40px;
  }
  .client-area .client-map-img {
    top: -9%;
  }
  .searc-filed-cart-area .search-filter-box {
    gap: 15px;
  }
  .searc-filed-cart-area {
    padding: 15px;
    bottom: -101px;
    width: 89%;
  }
  .hero-area .hero-content-text {
    margin-top: -25px;
  }
  .form-label {
    margin-bottom: 0.5rem;
    font-size: 9px;
  }
  .client-area {
    padding-top: 40px;
  }
  .section-title-area h2 {
    font-size: 40px;
  }
  .single-photograph .photo-title-area h2 {
    font-size: 44px;
    line-height: 60px;
  }
  .skill-program-card {
    flex: 0 0 50%;
    width: 50%;
  }
  .skill-program-card {
    margin-bottom: 0;
  }
  .learn-skill-area .skill-shape-2 {
    top: 1%;
  }
  .learn-skill-area .skill-content-text h2 {
    font-size: 42px;
  }
  .single-counter-card .counter-info h2 {
    color: #fff;
    font-size: 58px;
  }
  .single-counter-card .counter-info {
    padding: 60px 55px;
  }
  .blog-img img {
    width: 100%;
  }
  .b-shape-img img {
    width: 100%;
  }
  .footer-wrap .footer-content .footer-contact-info ul li {
    margin-bottom: 10px;
  }
  .header-2-contact-info {
    flex: 0 0 33.33%;
    width: 40%;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text p {
    font-size: 12px;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text h6 {
    font-size: 13px;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .contact-info-icon {
    margin-right: 15px;
  }
  .header-area.home-2 {
    top: 55px;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::before {
    right: 20%;
  }
  .care-program-area .single-care-card .care-card-img-content .care-img::after {
    left: 20%;
  }
  .quick-links-nav:first-child {
    margin-top: 0;
  }
  .footer-wrap .footer-content .footer-desc {
    font-size: 20px;
  }
  .care-program-area .single-care-card .care-shape {
    width: 100%;
  }
  .knowledge-cta-area-2 .knowledge-cta-content h2 {
    font-size: 32px;
  }
  .knowledge-cta-content {
    margin-top: 50px;
  }
  .client-area-home-2 .client-image-home-2::after {
    left: -18%;
  }
  .upcoming-event-area .single-event-card .event-card-content h4 {
    font-size: 22px;
  }
  .certificate-area .gra-cert-image-area .play-btn {
    right: 12%;
    bottom: -3%;
  }
  .certificate-area .gra-shape-1 {
    top: 8%;
    left: 14%;
  }
  .certificate-area .gra-shape-2 {
    left: -2%;
  }
  .hero-area-2 .hero-content-text h2 {
    font-size: 60px;
    line-height: 73px;
  }
  .hero-area-2 {
    height: 900px;
  }
  .hero-area-2 .hero-2-shape-3 {
    right: -23%;
  }
  .section-title-area h2 {
    font-size: 40px;
  }
  .certificate-area .certi-title-area {
    margin-top: 300px;
  }
  .certificate-area .certi-title-area h2 {
    font-size: 46px;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text::after {
    bottom: -48%;
    width: 50%;
    left: 0;
    height: 160%;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text::before {
    bottom: -51%;
    right: -31px;
    width: 56%;
    height: 160%;
  }
  .blog-area .single-blog-artical .single-blog-card .date-badge {
    top: -84%;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text {
    padding: 30px 70px;
  }
  .blog-area .single-blog-artical .single-blog-card .blog-content-text h3 {
    font-size: 26px;
  }
  .blog-area .single-blog-artical .single-blog-card .hover-blog-content-img .hover-blog-content h3 {
    font-size: 25px;
  }
  .hover-blog-content-img .b-shape-img .date-badge {
    top: -19% !important;
  }
  .contact-info {
    margin-bottom: 0;
    margin-top: 0;
  }
  .top-header {
    height: 66px;
  }
  .top-social-conatct {
    margin-top: 0;
  }
  .contact-info {
    margin-bottom: 0;
    margin-top: 0;
  }
  .fav-course-area-3 .tab-btn-img-text .course-title-4 {
    font-size: 16px;
  }
  .fav-course-area-3 .course-tab-heading li {
    flex: 0 0 50%;
  }
  .fav-course-area-3 .tab-btn-img-text .tab-btn-img {
    flex: 0 0 35px;
  }
  .header-area {
    top: 58px;
  }
  .fav-course-area-3 .tab-btn-img-text p {
    font-size: 11px;
    flex: 0 0 30%;
    margin-bottom: 0;
  }
  .header-area.sticky {
    top: 0;
  }
  .header-area.sticky .menu-area- {
    padding: 0 15px;
  }
  .top-serach-bar {
    flex: 0 0 50%;
    padding-right: 0;
  }
  .top-header.three .top-social-conatct-3 .header-3-bread li {
    font-size: 12px;
    margin-right: 0;
  }
  .footer-logo img {
    width: 170px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .menu-area- {
    padding: 10px 15px;
  }
  .header-area.home-2 .navbar {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
  .contact-info {
    justify-content: flex-start;
  }
  .top-social-conatct {
    justify-content: end;
  }
  .section-title-area h2 {
    font-size: 39px;
  }
  .about-us-area .about-us-title-img h2 {
    font-size: 35px;
  }
  .hero-area .wel-shape-3 {
    right: 6%;
    top: 29%;
  }
  .hero-area-2 .hero-content-text h2 {
    font-size: 65px;
    line-height: 80px;
  }
  .hero-area-2 {
    height: 1150px;
  }
  .knowledge-cta-area .knowledge-cta-content-img {
    padding: 0 10%;
  }
  .team-area .team-block .single-team-card .contact-social-icon .share-icon .share--icon {
    bottom: 24%;
  }
  .about-us-area .img-badge {
    flex: 0 0 38%;
    width: 38%;
  }
  .about-us-area .about-desc-info-area p {
    font-size: 14px;
    line-height: 27px;
  }
  .about-us-area .about-us-title-img .img-badge-text p {
    font-size: 13px;
  }
  .about-us-area .about-us-title-img .img-badge-text p a {
    font-size: 15px;
  }
  .about-us-area .about-desc-info-area .about-content-card-area .about-single-card p {
    font-size: 13px;
    line-height: 23px;
  }
  .about-us-area .about-desc-info-area h6 {
    font-size: 18px;
  }
  .about-shape-3 img {
    width: 65%;
  }
  .about-us-area .about-us-title-img .about-img .about-img-2 {
    top: -3px;
    right: 0;
  }
  .about-us-area .about-shape-3 {
    right: 0;
  }
  .about-us-area .about-us-title-img .about-img {
    margin-top: 30px;
    position: relative;
  }
  .single-counter-card .counter-info {
    padding: 95px 55px;
  }
  .hero-content-text h2 {
    font-size: 64px;
    line-height: 85px;
  }
  .hero-content-text p {
    line-height: 26px;
    font-size: 17px;
  }
  .hero-area {
    height: 1200px;
  }
  .hero-content-text {
    transform: translateY(-75%);
  }
  .hero-content-text {
    transform: translateY(-75%);
  }
  .top-serach-bar .form-control {
    height: 38px;
  }
  .top-serach-bar .search-btn {
    right: 49px;
  }
  .contact-info a span {
    font-size: 12px;
  }
  .login-btn {
    height: 26px;
    width: 80px;
    font-size: 12px;
  }
  .counter-upaarea-2::after {
    right: -179px;
  }
  .knowledge-cta-area .knowledge-cta-content-img .knowledge-cta-text h2 {
    font-size: 48px;
  }
  .section-title-area h2 {
    font-size: 48px;
  }
  .mobile-ver {
    display: none;
  }
  .learn-skill-area .skill-content-text h2 {
    font-size: 34px;
  }
  .learn-skill-area .skill-content-text p {
    font-size: 15px;
    line-height: 27px;
  }
  .skill-program-card {
    flex: 0 0 50%;
    width: 50%;
  }
  .skill-program-card {
    margin-bottom: 0;
  }
  .learn-skill-area .skill-content-text .skill-program-card .program-icon span {
    font-size: 42px;
  }
  .learn-skill-area .skill-content-text .skill-program-card .skill-program-text h4 {
    font-size: 22px;
  }
  .learn-skill-area .skill-bg-img {
    transform: translateY(69%);
  }
  .learn-skill-area {
    padding-bottom: 140px;
  }
  .section-title-area h2 {
    font-size: 39px;
  }
  .header-area {
    top: 55px;
  }
  .header-area .navbar {
    padding-top: 0;
    padding-bottom: 0;
  }
  .header-area {
    top: 48px;
  }
  .top-header {
    height: 46px;
  }
  .top-serach-bar .search-btn {
    right: 25px;
  }
  .counter-upaarea-2 .counter-shape-2 {
    right: -12%;
  }
  .counter-upaarea-2::after {
    right: -170px;
  }
  .counter-upaarea-2::after {
    width: 110px;
  }
  .section-title-area h2 {
    font-size: 46px;
  }
  .certificate-area .gra-cert-image-area .play-btn {
    right: 28%;
    bottom: -12%;
  }
  .certificate-area .certi-title-area {
    padding-left: 0;
    margin-top: 320px;
  }
  .knowledge-cta-area-2 .knowledge-per-img {
    top: 64%;
  }
  .certificate-area .certi-title-area h2 {
    font-size: 46px;
  }
  .certificate-area .certi-single-card .certi-card-info h4 {
    font-size: 24px;
  }
  .knowledge-cta-area-2 .knowledge-cta-content .skill-program-card:first-child {
    margin-right: 0;
  }
  .knowledge-cta-area-2 .knowledge-cta-content .skill-program-card .skill-program-text h4 {
    font-size: 19px;
  }
  .knowledge-cta-area-2 .knowledge-cta-content .skill-program-card .program-icon span {
    font-size: 36px;
  }
  .knowledge-cta-area-2 .knowledge-image::after {
    left: 4%;
  }
  .knowledge-cta-content {
    margin-top: 0;
  }
  .section-title-area h2 {
    font-size: 41px;
  }
  .form-label {
    margin-bottom: 0.5rem;
    font-size: 12px;
  }
  .searc-filed-cart-area {
    bottom: -132px;
    width: 90%;
  }
  .searc-filed-cart-area .search-filter-box {
    gap: 15px;
  }
  .blog-area-home-2 .blog-card-home-2 .blog-conetnt-card-2 h3 {
    font-size: 19px;
  }
  .client-area-home-2 .client-image-home-2::after {
    left: -76%;
  }
  .upcoming-event-area .single-event-card .event-card-content h4 {
    font-size: 17px;
  }
  .upcoming-event-area .single-event-card {
    margin-bottom: 30px;
  }
  .header-2-contact-info {
    flex: 0 0 33.33%;
    width: 33.333%;
  }
  .header-2-contact-info {
    margin-bottom: 0;
  }
  .header-area.home-2 {
    top: 70px;
  }
  .hero-area-2 {
    height: 1050px;
  }
  .hero-area-2 .hero-content-text {
    transform: translateY(30%);
  }
  .section-title-area h2 {
    font-size: 50px;
  }
  .event-content-text {
    margin-top: 0;
  }
  .section-title-area h2 {
    font-size: 38px;
  }
  .top-header-4-content {
    flex-wrap: nowrap;
  }
  .top-header-4-content .dropdown-search-menu {
    flex: 0 0 40%;
  }
  .cart-login--area-4 {
    position: relative;
    right: 0;
    top: 0;
  }
  .top-serach-bar-four .form-control {
    height: 32px;
    width: 100%;
  }
  .top-header.four {
    height: 45px;
  }
  .top-header-4-content .dropdown-search-menu {
    margin-top: 0;
  }
  .header-area.four {
    top: 45px;
    padding: 10px 0;
  }
  .hero-area-4 .hero-content-text h2 {
    font-size: 50px;
  }
  .hero-area-4 .wel-shape-1 {
    right: -13%;
    bottom: -63%;
  }
  .heading-title-4 h3 {
    font-size: 36px;
  }
  .heading-btn-4 a {
    font-size: 14px;
    padding: 7px 20px;
  }
  .certificate-area .gra-shape-2 {
    top: 2%;
    left: 4%;
  }
  .certificate-area .gra-shape-1 {
    top: 5%;
    left: 10%;
  }
  .client-area-home-2 .client-image-home-2 {
    display: none;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text h6 {
    font-size: 12px;
  }
  .top-header.home-2 .header-2-contact-area .header-2-contact-info .header-2-contact-text p {
    font-size: 11px;
  }
  .header-area.home-2 {
    top: 55px;
  }
  .breadcrumb-area-2 .breadcrumb-conetnt {
    margin-top: 80px;
  }
  .popular-cate-2-area.pricing-page {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .popular-cate-2-area.pricing-page .popular-cate-2-area .cate-2-shape {
    display: none;
  }
  .log-regi-area {
    padding-bottom: 90px;
  }
  .log-regi-area .log-area {
    margin-bottom: 50px;
  }
  .top-serach-bar {
    flex: 0 0 50%;
    padding-right: 0;
  }
  .hero-area .wel-shape-4 {
    display: none;
  }
  .hero-area .hero-content-text {
    margin-top: 0;
  }
  .client-area-3 .single-client-3 {
    padding-left: 0;
    margin-top: 10px;
  }
  .client-area-3 .single-client-3 .footer-icon-home-3 {
    top: -11%;
  }
  .footer-logo img {
    width: 170px;
  }
  .client-area-3 .client-nav li {
    border: 5px solid #fff;
  }
}/*# sourceMappingURL=style.css.map */
