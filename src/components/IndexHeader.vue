<script setup lang="ts">
import {useHeaderSticky} from "@/utils/common.js";
import {onMounted} from "vue";

const routes =[
  {name:'首页',path:'/',children:
     [
        {name:'儿童教育',path:'/kid'},
        {name:'课程市场',path:'/market'},
        {name:'大学经典',path:'/university'}
     ]
  },
  {name: '课程列表',children:
        [
          {name:'全部课程',path:'/course/list'},
          {name:'课程详情',path:'/course-details'}
        ]
  },
  {name:'关于我们',path: '/teacher',children:
        [
          {name:'师资团队',path:'/teacher'},
          {name:'加入我们',path:'/join'}
        ]
  },
  {name:'编程博客',path: '/blog'}
]

onMounted(useHeaderSticky);
</script>

<template>
  <!-- Top Header -->
  <div class="top-header">
    <!-- Container -->
    <div class="container h-100">
      <div class="row h-100 align-items-center">
        <div class="col-md-6 col-lg-7">
          <div class="contact-info d-flex align-items-center">
            <a href="mailto:info@doorsoft"><span class="icon-mail"></span> <span
                class="text-white contact-desc"><EMAIL></span></a>
            <a href=""><span class="icon-phone-alt-solid-11"></span> <span class="text-white contact-desc">123 - 456 -
                  7890</span></a>
          </div>
        </div>

        <div class="col-md-6 col-lg-5">
          <div class="d-flex align-items-center top-social-conatct">
            <!-- Dropdown -->
            <div class="top-social-icon">
              <ul class="header-icon">
                <li><span class="text-white">Follow Us :</span></li>
                <li><a href="#"><span class="icon-facebook-f"></span></a></li>
                <li><a href="#"><span class="icon-linkedin-in"></span></a></li>
                <li><a href="#"><span class="icon-twitter"></span></a></li>
                <li><a href="#"><span class="icon-instagram"></span></a></li>
              </ul>
            </div>
            <div class="contact-info">
              <a class="login-btn" href="#"><span class="icon-user-plus-solid-1"></span> Sign Up</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Header -->
  <header class="header-area home-2 bg--navy" id="header">
    <nav class="navbar navbar-expand-lg">
      <div class="container menu-area-">
        <!-- Navbar Brand -->
        <div class="mobile-menu">
          <a class="navbar-brand" href="index.html">
            <img src="@/assets/img/core-img/logo-2.png" alt="Brand">
          </a>
        </div>

        <!-- Navbar Toggler -->
        <button class="navbar-toggler" id="navbarToggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent" aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
          <span class="icon-menu"></span>
        </button>

        <!-- Navbar -->
        <div class="collapse navbar-collapse" id="navbarContent">
          <ul class="navbar-nav navbar-nav-scroll mb-3 mb-lg-0" id="SupportHiveNav">

            <!-- 循环遍历主导航菜单项 -->
            <li v-for="item in routes"
                :key="item.path"

                :class="item.children ? 'dropdown-list' : ''">

                <!-- 主菜单链接 -->
                <router-link :to="item.path">{{ item.name }}</router-link>

                <!-- 下拉箭头图标 - 仅在有子菜单时显示 -->
                <div v-if="item.children" class="dropdown-toggler">
                  <i class="icon-down-arrow-1"></i>
                </div>

                <!-- 子菜单列表 - 仅在有子菜单时渲染 -->
                <ul v-if="item.children">
                  <!-- 循环遍历子菜单项 -->
                  <li v-for="child in item.children" :key="child.path">
                    <!-- 子菜单链接 -->
                    <router-link :to="child.path">{{ child.name }}</router-link>
                  </li>
                </ul>
            </li>

<!--            <li class="dropdown-list">
              <a href="#">Home</a>
              <div class="dropdown-toggler"><i class="icon-down-arrow-1"></i></div>
              <ul>
                <li><a href="index.html">Education</a></li>
                <li><a href="index-kid-education.html">KidEducation</a></li>
                <li><a href="index-marketplaxe.html">Marketplace</a></li>
                <li><a href="index-university.html">University Classic</a></li>
              </ul>
            </li>

            <li class="dropdown-list">
              <a href="#">Course</a>
              <div class="dropdown-toggler"><i class="icon-down-arrow-1"></i></div><ul>
              <li><a href="course-list.html">Course list</a></li>
              <li><a href="all-course.html">all Course</a></li>
              <li><a href="course-details.html">Course Details</a></li>
            </ul>
            </li>

            <li class="dropdown-list">
              <a href="#">Pages</a>
              <div class="dropdown-toggler"><i class="icon-down-arrow-1"></i></div><ul>
              <li><a href="about.html">About</a></li>
              <li><a href="gallery.html">Gallery</a></li>
              <li><a href="faq.html">Faq</a></li>
              <li><a href="pricing.html">Price</a></li>
              <li><a href="team.html">Team</a></li>
              <li><a href="contact.html">Contact</a></li>
              <li><a href="login.html">Login</a></li>
            </ul>
            </li>
            <li class="dropdown-list">
              <a href="#">Event</a>
              <div class="dropdown-toggler"><i class="icon-down-arrow-1"></i></div><ul>
              <li><a href="event.html">Event list</a></li>
              <li><a href="event-sidebar.html">Event Sidebar</a></li>
              <li><a href="event-details.html">Event Details</a></li>
            </ul>
            </li>
            <li class="dropdown-list">
              <a href="#">Shop</a>
              <div class="dropdown-toggler"><i class="icon-down-arrow-1"></i></div><ul>
              <li><a href="shop.html">Shop</a></li>
              <li><a href="shop-details.html">Shop Details</a></li>
              <li><a href="cart.html">Cart</a></li>
              <li><a href="checkout.html">Checkout</a></li>
            </ul>
            </li>

            <li class="dropdown-list">
              <a href="#">Blog</a>
              <div class="dropdown-toggler"><i class="icon-down-arrow-1"></i></div><ul>
              <li><a href="blog.html">Blog</a></li>
              <li><a href="blog-details.html">Blog Details</a></li>
            </ul>
            </li>-->
          </ul>

          <div class="cart-search-area ml-auto d-flex align-items-center">
            <div class="search-home-2">
              <div class="header-action">
                <div class="search-toggle-open header-search">
                  <div class="search-icon">
                    <span class="icon-icon_search2"></span>
                  </div>
                </div>
              </div>
            </div>
            <div class="cart-btn-area relative">
              <a href="#" class="relative"><span class="icon-icon_cart_alt"></span></a>
              <div class="cart-badge bg--dark">0</div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  </header>

</template>

<style scoped>

</style>