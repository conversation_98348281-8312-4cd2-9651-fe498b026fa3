<script setup lang="ts">
import {useHeaderSticky} from "@/utils/common.js";
import {onMounted} from "vue";
import {useAccount} from "@/stores/user.js";

const account = useAccount();

const routes =[
  {name:'首页',path:'/',children:
     [
        {name:'儿童教育',path:'/kid'},
        {name:'课程市场',path:'/market'},
        {name:'大学经典',path:'/university'}
     ]
  },
  {name: '课程列表',children:
        [
          {name:'全部课程',path:'/course/list'},
          {name:'课程详情',path:'/course-details'}
        ]
  },
  {name:'关于我们',path: '/teacher',children:
        [
          {name:'师资团队',path:'/teacher'},
          {name:'加入我们',path:'/join'}
        ]
  },
  {name:'编程博客',path: '/blog'}
]

onMounted(useHeaderSticky);
</script>

<template>
  <!-- Header -->
  <header class="header-area home-2 bg--navy" id="header">
    <nav class="navbar navbar-expand-lg">
      <div class="container menu-area-">
        <!-- Navbar Brand -->
        <div class="mobile-menu">
          <a class="navbar-brand" href="index.html">
            <img src="@/assets/img/core-img/logo-2.png" alt="Brand">
          </a>
        </div>

        <!-- Navbar Toggler -->
        <button class="navbar-toggler" id="navbarToggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent" aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
          <span class="icon-menu"></span>
        </button>

        <!-- Navbar -->
        <div class="collapse navbar-collapse" id="navbarContent">
          <ul class="navbar-nav navbar-nav-scroll mb-3 mb-lg-0" id="SupportHiveNav">

            <!-- 循环遍历主导航菜单项 -->
            <li v-for="item in routes"
                :key="item.path"

                :class="item.children ? 'dropdown-list' : ''">

                <!-- 主菜单链接 -->
                <router-link :to="item.path">{{ item.name }}</router-link>

                <!-- 下拉箭头图标 - 仅在有子菜单时显示 -->
                <div v-if="item.children" class="dropdown-toggler">
                  <i class="icon-down-arrow-1"></i>
                </div>

                <!-- 子菜单列表 - 仅在有子菜单时渲染 -->
                <ul v-if="item.children">
                  <!-- 循环遍历子菜单项 -->
                  <li v-for="child in item.children" :key="child.path">
                    <!-- 子菜单链接 -->
                    <router-link :to="child.path">{{ child.name }}</router-link>
                  </li>
                </ul>
            </li>
          </ul>

          <div class="cart-search-area ml-auto d-flex align-items-center">
            <div class="search-home-2">
              <div class="header-action">
                <div class="search-toggle-open header-search">
                  <div class="search-icon">
                    <span class="icon-icon_search2"></span>
                  </div>
                </div>
              </div>
            </div>
            <div class="cart-btn-area relative">
              <a href="#" class="relative"><span class="icon-icon_cart_alt"></span></a>
              <div href="/Cart" class="cart-badge bg--dark">{{ account.cart.count }}</div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  </header>
</template>

<style scoped>

</style>