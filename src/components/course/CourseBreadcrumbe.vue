<script setup lang="ts">

import router from "@/router/index.js";

defineProps({
  courseCount: {
    type: Number,
    default: 0
  }
})
</script>

<template>
    <div>
      <!-- 面包屑导航区域 -->
      <div class="breadcrumb-area relative">
        <div class="container h-100">
          <div class="row h-100 align-items-center">
            <div class="col-12">
              <div class="breadcrumb-conetnt">
                <ul class="bread-list">
                  <li>首页</li>
                  <li><i class="icon-down-arrow-11"></i></li>
                  <li>课程</li>
                  <li><i class="icon-down-arrow-11"></i></li>
                  <li>全部课程</li>
                </ul>
                <h4>全部课程</h4>
                <p>帮助初学者成为真正编程高手的精品课程。</p>
              </div>
            </div>
          </div>
        </div>
        <!-- 筛选搜索区域 -->
        <div class="grid-filter-area relative">
          <div class="container">
            <div class="row">
              <div class="col-12">
                <!-- 筛选器 -->
                <div class="grid-filter-search relative d-flex align-items-center justify-content-between">
                  <div class="grid-filter-btn d-flex align-items-center">
                    <div class="grid-filter-btn-list">
                      <button class="grid-btn boxButton toggle-btn" @click="router.push('/course/list')">
                        <i class="icon-mingcute_grid-line"></i>&nbsp;
                        <span>网格视图</span></button>
                      <button class="list-btn" @click="router.push('/course/grid')">
                        <i class="icon-cil_list"></i>&nbsp;
                        <span>列表视图</span></button>
                    </div>
                    <p class="mb-0">已上架 {{ courseCount }} 个课程 ，欢迎选购</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<style scoped>

</style>