/**
 * 课程测试数据
 * 用于快速填充课程列表和测试功能
 */

export const courseTestData = [
  {
    id: 1,
    title: "JavaScript零基础入门",
    instructor: "张老师",
    description: "从零开始学习JavaScript编程，掌握前端开发的核心技能，适合完全没有编程基础的初学者。",
    details: "本课程将带您从JavaScript基础语法开始，逐步学习变量、函数、对象、数组等核心概念，最终能够独立开发简单的网页交互功能。课程包含大量实战练习，确保学以致用。",
    lessonCount: 24,
    price: 299,
    duration: "36小时",
    maxStudents: 100,
    category: "前端开发",
    level: "初级",
    rating: 4.8,
    studentCount: 1250,
    image: "@/assets/img/bg-img/inner/list-1.png"
  },
  {
    id: 2,
    title: "Vue.js全栈开发实战",
    instructor: "李老师",
    description: "深入学习Vue.js框架，从基础到高级，包含Vue Router、Vuex状态管理等核心技术。",
    details: "全面掌握Vue.js生态系统，学习组件化开发、路由管理、状态管理、项目构建等技能。课程结合实际项目案例，让您能够独立开发完整的前端应用。",
    lessonCount: 32,
    price: 499,
    duration: "48小时",
    maxStudents: 80,
    category: "前端开发",
    level: "中级",
    rating: 4.9,
    studentCount: 890,
    image: "@/assets/img/bg-img/inner/list-2.jpg"
  },
  {
    id: 3,
    title: "Python数据分析与机器学习",
    instructor: "王老师",
    description: "使用Python进行数据分析和机器学习，掌握NumPy、Pandas、Scikit-learn等核心库。",
    details: "从Python基础开始，学习数据处理、数据可视化、统计分析、机器学习算法等技能。课程包含真实数据集分析案例，培养数据科学思维。",
    lessonCount: 28,
    price: 599,
    duration: "42小时",
    maxStudents: 60,
    category: "数据科学",
    level: "中级",
    rating: 4.7,
    studentCount: 567,
    image: "@/assets/img/bg-img/inner/list-3.png"
  },
  {
    id: 4,
    title: "React Native移动应用开发",
    instructor: "陈老师",
    description: "使用React Native开发跨平台移动应用，一套代码同时支持iOS和Android。",
    details: "学习React Native框架，掌握移动应用开发的核心技能。包含导航、状态管理、原生模块调用、应用发布等完整开发流程。",
    lessonCount: 30,
    price: 699,
    duration: "45小时",
    maxStudents: 50,
    category: "移动开发",
    level: "中级",
    rating: 4.6,
    studentCount: 423,
    image: "@/assets/img/bg-img/inner/course-1.jpg"
  },
  {
    id: 5,
    title: "Node.js后端开发实战",
    instructor: "刘老师",
    description: "全面学习Node.js后端开发，包含Express框架、数据库操作、API设计等核心技能。",
    details: "从Node.js基础开始，学习服务器开发、数据库设计、RESTful API、身份验证、部署上线等完整后端开发技能。",
    lessonCount: 26,
    price: 549,
    duration: "39小时",
    maxStudents: 70,
    category: "后端开发",
    level: "中级",
    rating: 4.8,
    studentCount: 678,
    image: "@/assets/img/bg-img/inner/course-2.jpg"
  },
  {
    id: 6,
    title: "UI/UX设计从入门到精通",
    instructor: "赵老师",
    description: "学习现代UI/UX设计理念和工具，掌握Figma、Sketch等设计软件的使用。",
    details: "全面学习用户界面和用户体验设计，包含设计理论、色彩搭配、排版布局、原型制作、用户研究等核心技能。",
    lessonCount: 22,
    price: 399,
    duration: "33小时",
    maxStudents: 90,
    category: "设计",
    level: "初级",
    rating: 4.5,
    studentCount: 789,
    image: "@/assets/img/bg-img/inner/course-3.jpg"
  },
  {
    id: 7,
    title: "Java Spring Boot企业级开发",
    instructor: "孙老师",
    description: "学习Java Spring Boot框架，掌握企业级应用开发的核心技术和最佳实践。",
    details: "深入学习Spring Boot生态系统，包含Spring MVC、Spring Data、Spring Security等核心模块，结合实际企业项目案例。",
    lessonCount: 35,
    price: 799,
    duration: "52小时",
    maxStudents: 40,
    category: "后端开发",
    level: "高级",
    rating: 4.9,
    studentCount: 345,
    image: "@/assets/img/bg-img/inner/course-4.jpg"
  },
  {
    id: 8,
    title: "微信小程序开发实战",
    instructor: "周老师",
    description: "从零开始学习微信小程序开发，掌握小程序的核心API和开发技巧。",
    details: "全面学习微信小程序开发技术，包含页面设计、数据绑定、网络请求、支付功能、发布上线等完整开发流程。",
    lessonCount: 20,
    price: 349,
    duration: "30小时",
    maxStudents: 120,
    category: "移动开发",
    level: "初级",
    rating: 4.4,
    studentCount: 1100,
    image: "@/assets/img/bg-img/inner/course-5.jpg"
  },
  {
    id: 9,
    title: "Docker容器化部署实战",
    instructor: "吴老师",
    description: "学习Docker容器技术，掌握应用容器化部署和微服务架构设计。",
    details: "深入学习Docker容器技术，包含镜像制作、容器编排、Kubernetes集群管理、CI/CD流水线等现代化部署技术。",
    lessonCount: 18,
    price: 459,
    duration: "27小时",
    maxStudents: 60,
    category: "运维部署",
    level: "中级",
    rating: 4.7,
    studentCount: 234,
    image: "@/assets/img/bg-img/inner/course-6.jpg"
  },
  {
    id: 10,
    title: "区块链技术与智能合约开发",
    instructor: "郑老师",
    description: "学习区块链核心技术，掌握以太坊智能合约开发和DApp应用构建。",
    details: "全面了解区块链技术原理，学习Solidity智能合约编程、Web3.js交互、去中心化应用开发等前沿技术。",
    lessonCount: 25,
    price: 899,
    duration: "37小时",
    maxStudents: 30,
    category: "区块链",
    level: "高级",
    rating: 4.6,
    studentCount: 156,
    image: "@/assets/img/bg-img/inner/course-7.jpg"
  }
];

/**
 * 课程分类数据
 */
export const courseCategories = [
  "前端开发",
  "后端开发", 
  "移动开发",
  "数据科学",
  "设计",
  "运维部署",
  "区块链"
];

/**
 * 课程难度级别
 */
export const courseLevels = [
  "初级",
  "中级", 
  "高级"
];

/**
 * 讲师数据
 */
export const instructors = [
  "张老师",
  "李老师",
  "王老师", 
  "陈老师",
  "刘老师",
  "赵老师",
  "孙老师",
  "周老师",
  "吴老师",
  "郑老师"
];
