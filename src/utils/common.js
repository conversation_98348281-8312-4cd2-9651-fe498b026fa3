
export function useHeaderSticky(){
    // 获取DOM元素，避免依赖全局变量
    const header = document.querySelector('.header') || document.querySelector('#header');
    const navbarToggler = document.querySelector('.navbar-toggler') || document.querySelector('#navbarToggler');

    // 检查必要的DOM元素是否存在
    if (!header) {
        console.warn('Header element not found. Please ensure you have an element with class "header" or id "header"');
        return;
    }

    if (!navbarToggler) {
        console.warn('Navbar toggler element not found. Please ensure you have an element with class "navbar-toggler" or id "navbarToggler"');
        return;
    }

    // 检查header元素是否存在，避免在没有header时执行后续代码
    if (header) {
        // 记录上一次的滚动位置（当前未使用，可用于滚动方向检测）
        // 可以在未来版本中用于实现滚动方向判断，如向上滚动时显示header，向下滚动时隐藏
        let prevScrollPosition = window.pageYOffset;

        /**
         * 粘性头部处理函数
         * 根据页面滚动位置动态添加或移除sticky样式类
         *
         * @description 该函数会检查当前页面的垂直滚动距离：
         * - 超过70px：添加'sticky'类，使header固定在页面顶部
         * - 小于等于70px：移除'sticky'类，恢复header的原始定位
         *
         * @constant {number} STICKY_THRESHOLD - 触发粘性效果的滚动阈值（70px）
         */
        function stickyHeader() {
            // 获取当前页面的垂直滚动距离（从页面顶部开始计算）
            const currentScrollY = window.pageYOffset;

            // 定义触发粘性效果的滚动阈值
            const STICKY_THRESHOLD = 70;

            // 当页面向下滚动超过阈值时
            if (currentScrollY > STICKY_THRESHOLD) {
                // 添加sticky类，通常对应以下CSS效果：
                // - position: fixed; 固定定位
                // - top: 0; 固定在页面顶部
                // - background-color: 添加背景色
                // - box-shadow: 添加阴影效果
                header.classList.add("sticky");
            } else {
                // 滚动距离小于等于阈值时，移除sticky类
                // 恢复header的原始样式（通常是相对定位或绝对定位）
                header.classList.remove("sticky");
            }
        }

        // 页面加载完成时执行一次，确保初始状态正确
        // 这样可以避免页面刷新后滚动位置保持但样式未正确应用的问题
        window.addEventListener('load', stickyHeader);

        // 监听页面滚动事件，实时更新头部样式
        // 每次滚动都会触发stickyHeader函数，动态调整header样式
        window.addEventListener('scroll', stickyHeader);

        // 移动端菜单切换功能
        // 为移动端汉堡菜单按钮添加点击事件监听器
        navbarToggler.addEventListener('click', function () {
            // 切换mobile-menu-opened类，用于控制移动端菜单的显示/隐藏
            // 配合CSS可实现菜单的滑入滑出、淡入淡出等动画效果
            header.classList.toggle("mobile-menu-opened");
        });
    }
}

/**
 * 回到顶部功能
 * 当页面滚动超过指定距离时显示回到顶部按钮，点击按钮平滑滚动到页面顶部
 * @function useBackToTop
 * @description 监听页面滚动事件，控制回到顶部按钮的显示/隐藏，并处理点击事件
 */
export function useBackToTop() {
    // 获取回到顶部按钮元素
    let scrollButton = document.getElementById('scrollToTop');

    // 设置触发显示按钮的滚动距离阈值（像素）
    let topdistance = 600;

    // 确保按钮元素存在
    if (scrollButton) {
        // 监听页面滚动事件
        window.addEventListener('scroll', function () {
            // 检查页面滚动距离是否超过阈值
            // 兼容不同浏览器的滚动距离获取方式
            if (document.body.scrollTop > topdistance || document.documentElement.scrollTop > topdistance) {
                // 滚动距离超过阈值，显示回到顶部按钮
                scrollButton.classList.add('scrolltop-show');
                scrollButton.classList.remove('scrolltop-hide');
            } else {
                // 滚动距离未超过阈值，隐藏回到顶部按钮
                scrollButton.classList.add('scrolltop-hide');
                scrollButton.classList.remove('scrolltop-show');
            }
        });

        // 监听回到顶部按钮点击事件
        scrollButton.addEventListener('click', function () {
            // 平滑滚动到页面顶部
            window.scrollTo({
                top: 0,        // 滚动到顶部位置
                left: 0,       // 水平位置保持不变
                behavior: 'smooth'  // 使用平滑滚动动画
            });
        });
    }
}