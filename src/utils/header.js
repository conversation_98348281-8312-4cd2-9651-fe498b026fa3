/**
 * 头部导航栏粘性效果和移动端菜单切换功能
 * 当页面滚动超过指定距离时，导航栏会变为固定定位（粘性效果）
 * 同时处理移动端菜单的显示/隐藏切换
 *
 * @description 此函数实现了以下功能：
 * 1. 监听页面滚动事件，当滚动距离超过70px时添加sticky样式
 * 2. 监听移动端菜单切换按钮，控制菜单的显示/隐藏
 * 3. 页面加载时初始化头部状态
 *
 * @requires header - 全局变量，头部导航栏DOM元素
 * @requires navbarToggler - 全局变量，移动端菜单切换按钮DOM元素
 *
 * @example
 * // 在页面加载后调用
 * document.addEventListener('DOMContentLoaded', () => {
 *     useHeaderSticky();
 * });
 *
 * @example
 * // 配合CSS样式使用
 * // .header.sticky { position: fixed; top: 0; background: white; }
 * // .header.mobile-menu-opened .nav-menu { display: block; }
 *
 * @since 1.0.0
 * <AUTHOR>
 */
export function useHeaderSticky(){
    // 获取DOM元素，避免依赖全局变量
    const header = document.querySelector('.header') || document.querySelector('#header');
    const navbarToggler = document.querySelector('.navbar-toggler') || document.querySelector('#navbarToggler');

    // 检查必要的DOM元素是否存在
    if (!header) {
        console.warn('Header element not found. Please ensure you have an element with class "header" or id "header"');
        return;
    }

    if (!navbarToggler) {
        console.warn('Navbar toggler element not found. Please ensure you have an element with class "navbar-toggler" or id "navbarToggler"');
        return;
    }

    // 检查header元素是否存在，避免在没有header时执行后续代码
    if (header) {
        // 记录上一次的滚动位置（当前未使用，可用于滚动方向检测）
        // 可以在未来版本中用于实现滚动方向判断，如向上滚动时显示header，向下滚动时隐藏
        let prevScrollPosition = window.pageYOffset;

        /**
         * 粘性头部处理函数
         * 根据页面滚动位置动态添加或移除sticky样式类
         *
         * @description 该函数会检查当前页面的垂直滚动距离：
         * - 超过70px：添加'sticky'类，使header固定在页面顶部
         * - 小于等于70px：移除'sticky'类，恢复header的原始定位
         *
         * @constant {number} STICKY_THRESHOLD - 触发粘性效果的滚动阈值（70px）
         */
        function stickyHeader() {
            // 获取当前页面的垂直滚动距离（从页面顶部开始计算）
            const currentScrollY = window.pageYOffset;

            // 定义触发粘性效果的滚动阈值
            const STICKY_THRESHOLD = 70;

            // 当页面向下滚动超过阈值时
            if (currentScrollY > STICKY_THRESHOLD) {
                // 添加sticky类，通常对应以下CSS效果：
                // - position: fixed; 固定定位
                // - top: 0; 固定在页面顶部
                // - background-color: 添加背景色
                // - box-shadow: 添加阴影效果
                header.classList.add("sticky");
            } else {
                // 滚动距离小于等于阈值时，移除sticky类
                // 恢复header的原始样式（通常是相对定位或绝对定位）
                header.classList.remove("sticky");
            }
        }

        // 页面加载完成时执行一次，确保初始状态正确
        // 这样可以避免页面刷新后滚动位置保持但样式未正确应用的问题
        window.addEventListener('load', stickyHeader);

        // 监听页面滚动事件，实时更新头部样式
        // 每次滚动都会触发stickyHeader函数，动态调整header样式
        window.addEventListener('scroll', stickyHeader);

        // 移动端菜单切换功能
        // 为移动端汉堡菜单按钮添加点击事件监听器
        navbarToggler.addEventListener('click', function () {
            // 切换mobile-menu-opened类，用于控制移动端菜单的显示/隐藏
            // 配合CSS可实现菜单的滑入滑出、淡入淡出等动画效果
            header.classList.toggle("mobile-menu-opened");
        });
    }
}

/**
 * 使用说明和注意事项：
 *
 * 1. 依赖的全局变量：
 *    - header: 头部导航栏的DOM元素
 *    - navbarToggler: 移动端菜单切换按钮的DOM元素
 *
 * 2. 需要配合的CSS样式：
 *    .header.sticky {
 *        position: fixed;
 *        top: 0;
 *        left: 0;
 *        width: 100%;
 *        z-index: 1000;
 *        background-color: rgba(255, 255, 255, 0.95);
 *        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
 *        transition: all 0.3s ease;
 *    }
 *
 *    .header.mobile-menu-opened .nav-menu {
 *        display: block; // 或其他显示菜单的样式
 *    }
 *
 * 3. 性能优化建议：
 *    - 可以使用throttle函数限制scroll事件的触发频率
 *    - 可以使用Intersection Observer API替代scroll事件监听
 *
 * 4. 浏览器兼容性：
 *    - window.pageYOffset: IE9+
 *    - classList.add/remove/toggle: IE10+
 *    - addEventListener: IE9+
 *
 * @todo 未来可能的改进：
 * - 添加滚动方向检测，向上滚动时显示header，向下滚动时隐藏
 * - 添加防抖/节流优化滚动性能
 * - 支持自定义滚动阈值
 * - 添加动画过渡效果的配置选项
 */