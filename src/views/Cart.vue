<script setup lang="ts">
import bg from "@/assets/img/bg-img/home-3/bg.png";
import request from "@/net";
import {computed, onMounted, ref} from "vue";

const data = ref([]);
/*const total = computed(() => {
  data.value.map(item => item.count * item.price).reduce((a, b) => a + b, 0) });*/
const total = computed(() => {
  return data.value.map(item => item.count * item.price).reduce((a, b) => a + b, 0);
});

onMounted(
    request.get("/system/item/list").then(res => {
      data.value = res.data.rows;
    })
)
console.log(total.value);
</script>

<template>
  <div>
    <div id="smooth-content">
      <div class="breadcrumb-area-2 bg-overlay-3 relative bg-img" :style="{ backgroundImage: `url(${bg})`}">
        <div class="container h-100">
        <div class="row h-100 align-items-center">
                <div class="col-12">
              <div class="breadcrumb-conetnt">
                <ul class="bread-list">
                  <li><a href="#">首页</a></li>
                  <li><i class="icon-down-arrow-11"></i></li>
                  <li>购物车</li>
                </ul>
                <h4 class="mb-0">购物车</h4>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="product-cart-area">
        <div class="container">
          <div class="row">
            <div class="col-12">
              <div class="cart-table">
                <div class="table-responsive">
                  <table class="table">
                    <thead class="table-navy">
                    <tr><th></th>
                      <th></th>
                      <th>产品</th>
                      <th>价格</th>
                      <th>数量</th>
                      <th>小计</th>
                    </tr></thead>
                    <tbody>
                    <tr v-for="item in data" :key="item.id">
                      <td><span class="icon-icon_close_alt2"></span></td>
                      <td class="cart-pro-img"><img src="@/assets/img/bg-img/inner/shop-1.png" alt=""></td>
                      <td>{{ item.title }}</td>
                      <td>${{ item.price }}</td>
                      <td>
                        <div class="qty-pro-area">
                          <form class="cart-form">
                            <div class="order-plus-minus d-flex align-items-center">
                              <div class="quantity-button-handler">-</div>
                              <input class="form-control cart-quantity-input" type="text" name="quantity" :value="item.count">
                              <div class="quantity-button-handler">+</div>
                            </div>
                            <div class="fav-icon">
                              <i class="icon-icon_ribbon_alt"></i>
                            </div>
                          </form>
                        </div>
                      </td>
                      <td>${{ item.count * item.price }}</td>
                    </tr>
                    </tbody>
                  </table>
                </div>


              </div>
            </div>

            <div class="col-12">
              <div class="coupon-cart-btn-area">
                <div class="apply-coupon">
                  <div class="coupon-form">
                    <h5>有折扣码吗？</h5>
                    <form class="cop-form" action="#">
                      <input class="form-control" type="text" placeholder="折扣码">
                      <button class="btn-copon" type="submit">应用折扣</button>
                    </form>
                  </div>
                  <button type="submit" class="update-btn">更新购物车</button>
                </div>
              </div>
            </div>
          </div>

          <div class="row justify-content-end">
            <div class="col-lg-5 col-xl-4">
              <div class="cart-total-card">
                <h4>购物车总计</h4>

                <div class="cart-list">
                  <p>小计</p>
                  <p>${{ total }}</p>
                </div>

                <div class="cart-list">
                  <div class="cart-tax-pri">
                    <p>税</p>
                    <p>美国 (8.375%)</p>
                  </div>
                  <p>${{ total * 0.08375 }}</p>
                </div>

                <div class="total-order">
                  <h5>订单总计</h5>
                  <div class="total-order-desc">
                    <h6>$545.88</h6>
                    <span>不含税: $500.00</span>
                  </div>
                </div>

                <button type="submit" class="pro-btn mt-4"><span class="icon-padlock"></span> 前往结账</button>

              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <footer class="footer-wrap bg--dark">
        <!-- Shape -->
        <div class="f-shape-1 wow fadeInUp" data-wow-delay="900ms" style="visibility: visible; animation-delay: 900ms; animation-name: fadeInUp;">
          <img src="@/assets/img/bg-img/f-shap-1.png" alt="">
        </div>
        <!-- Shape -->
        <div class="f-shape-2 wow fadeInRight" data-wow-delay="700ms" style="visibility: visible; animation-delay: 700ms; animation-name: fadeInRight;">
          <img src="@/assets/img/bg-img/f-shap-2.png" alt="">
        </div>
        <!-- Shape -->
        <div class="f-shape-3 wow fadeInRight" data-wow-delay="500ms" style="visibility: hidden; animation-delay: 500ms; animation-name: none;">
          <img src="@/assets/img/bg-img/f-shape-3.png" alt="">
        </div>
        <div class="footer-content">
          <div class="container">
            <div class="row justify-content-between">
              <div class="col-12 col-sm-6 col-lg-3">
                <div class="me-4">
                  <a class="mb-4 d-block footer-logo" href="#">
                    <img src="@/assets/img/core-img/logo-2.png" alt="">
                  </a>
                  <p class="footer-desc">从我们这里获取18,000+最佳在线课程</p>

                  <div class="footer-contact-info wow fadeInLeft" data-wow-delay="700ms" style="visibility: hidden; animation-delay: 700ms; animation-name: none;">
                    <ul>
                      <li><a href="#"><span class="icon-facebook-f"></span></a></li>
                      <li><a href="#"><span class="icon-instagram"></span></a></li>
                      <li><a href="#"><span class="icon-linkedin-in"></span></a></li>
                      <li><a href="#"><span class="icon-instagram"></span></a></li>
                    </ul>
                  </div>
                </div>
              </div>

              <div class="col-12 col-sm-6 col-lg-2">
                <div class="quick-links-nav">
                  <h5 class="mb-3 text-white">探索</h5>
                  <span class="footer-shape-1"></span>
                  <span class="footer-shape-2"></span>
                  <ul>
                    <li><a href="#">GMAT辅导</a></li>
                    <li><a href="#">GRE辅导</a></li>
                    <li><a href="#">IELTS辅导</a></li>
                    <li><a href="#">TOEFL辅导</a></li>
                    <li><a href="#">SAT辅导</a></li>
                  </ul>
                </div>
              </div>

              <div class="col-12 col-sm-6 col-lg-2">
                <div class="quick-links-nav">
                  <h5 class="mb-3 text-white">链接</h5>
                  <ul>
                    <li><a href="#">SAT辅导</a></li>
                    <li><a href="#">IELTS辅导</a></li>
                    <li><a href="#">GRE辅导</a></li>
                    <li><a href="#">GMAT辅导</a></li>
                    <li><a href="#">TOEFL辅导</a></li>
                  </ul>
                </div>
              </div>

              <div class="col-12 col-sm-6 col-lg-3">
                <div class="quick-contact-nav">
                  <h5 class="mb-3 text-white">联系</h5>
                  <span class="footer-shape-1"></span>
                  <span class="footer-shape-2"></span>
                  <ul>
                    <li>
                      <p><span class="icon-incoming-call"></span> + 1 (246) 333-0088</p>
                    </li>
                    <li>
                      <p><span class="icon-email"></span> <EMAIL></p>
                    </li>
                    <li>
                      <p><span class="icon-map"></span> 4140 Parker Rd. Allentown, New Mexico
                        31134</p>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="copy-right-area">
          <div class="container">
            <div class="row">
              <div class="col-12">
                <div class="copy-right-text text-center wow fadeInDown" data-wow-delay="900ms" style="visibility: hidden; animation-delay: 900ms; animation-name: none;">
                  <p class="mb-0"> © Copyright 2024 by <a href="https://www.bootstrapmb.com">bootstrapMB</a></p>
                </div>
              </div>

            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>
</template>

<style scoped>

</style>