<script setup lang="ts">
import request from "@/net";
import {onMounted, reactive} from "vue";
import Swal from 'sweetalert2'
import router from "@/router";
import {setToken} from "@/utils/token";


const verify = reactive({
  img: "",
  uuid: ""
})
const form = reactive({
  username: "",
  password: "",
  code: "",
})

function refreshImage() {
  request.get("/captchaImage").then((data) => {
    verify.img = data.data.img
    verify.uuid = data.data.uuid
  })
}

function login() {
  form.uuid = verify.uuid
  request.post("/login", {
    username:form.username,
    password:form.password,
    code:form.code,
    uuid:verify.uuid
  }).then((data) => {
      if (data.data.code !== 200) {
        refreshImage();
        Swal.fire({
          title: "登录失败",
          text: data.data.msg,
          icon: "error",
          showConfirmButton: false,
          timer: 1500
        })
      } else {
        Swal.fire({
          title: "登录成功",
          text: "欢迎回来",
          icon: "success",
          showConfirmButton: false,
          timer: 1500
        }).then(() => {
          setToken(data.data.token)
          router.push("/")
        })
      }
  })
}

onMounted(refreshImage)
</script>

<template>
    <div>
      <div class="log-regi-area">
        <div class="container">
          <div class="row">
            <div style="width: 500px;margin: auto;">
              <div class="log-area">
                <h2>Login</h2>
                <div class="form-floating mb-3">
                  <input v-model="form.username" type="email" class="form-control" id="floatingInput1" placeholder="用户名或电子邮件地址 *">
                  <label class="lable-text" for="floatingInput1">用户名或电子邮件地址
                    *</label>
                </div>
                <div class="form-floating relative mb-4">
                  <input v-model="form.password" type="password" class="form-control relative" id="floatingPassword1" placeholder="密码 *">
                  <label class="lable-text" for="floatingPassword1">密码 *</label>

                  <div class="password-key">
                    <i class="icon-eye"></i>
                  </div>
                </div>
                <div class="row">
                  <div class="form-floating mb-5 col-8">
                    <input v-model="form.code" type="email" class="form-control" id="floatingInput2" placeholder="验证码">
                    <label class="lable-text" for="floatingInput2">验证码</label>
                  </div>
                  <div class="col-4">
                    <img data-v-d0e06bca="" :src="`data:image/gif;base64,${verify.img}`" class="login-code-img">
                  </div>
                </div>

                <div class="rem-forgot-btn">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="" id="rememberMe1" checked="">
                    <label class="form-check-label" for="rememberMe1">
                      记住我
                    </label>
                  </div>
                </div>
                <button type="submit" class="auth-btn w-100 mt-5" @click="login">登录</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<style scoped>

</style>