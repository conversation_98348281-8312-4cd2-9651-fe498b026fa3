<script setup lang="ts">

</script>

<template>
    <div>
      <div class="log-regi-area">
        <div class="container">
          <div class="row">
            <div class="col-lg-6">
              <div class="log-area">
                <h2>注册</h2>
                <div class="form-floating mb-5">
                  <input type="text" class="form-control" id="floatingInput6" placeholder="用户名 *">
                  <label class="lable-text" for="floatingInput6">用户名 *</label>
                </div>
                <div class="form-floating mb-5">
                  <input type="email" class="form-control" id="floatingInput3" placeholder="电子邮件地址 *">
                  <label class="lable-text" for="floatingInput3">电子邮件地址
                    *</label>
                </div>
                <div class="form-floating relative mb-4">
                  <input type="password" class="form-control relative" id="floatingPassword4" placeholder="密码 *">
                  <label class="lable-text" for="floatingPassword4">密码 *</label>

                  <div class="password-key">
                    <i class="icon-eye"></i>
                  </div>
                </div>

                <div class="mb-20 rem-forgot-btn">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="" id="rememberMe" checked="">
                    <label class="form-check-label" for="rememberMe">
                      记住我
                    </label>
                  </div>

                  <div class="forgot-pass">
                    <a href="#">忘记密码？</a>
                  </div>
                </div>

                <p class="auth-desc">您的个人数据将用于支持您在此网站上的体验，管理您的账户访问，以及其他在我们隐私政策中描述的目的 <a href="#">隐私政策</a></p>

                <button type="submit" class="auth-btn w-100 mt-5">注册</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<style scoped>

</style>