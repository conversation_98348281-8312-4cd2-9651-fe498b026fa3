<script setup lang="ts">
import {useRoute} from "vue-router";
import router from "@/router";
import request from "@/net";
import {createRandomInt} from "@/utils/data";
import bg from "@/assets/img/bg-img/home-3/bg.png";
import {ref} from "vue";

const data = ref([]);
const route = useRoute();
const id = route.params.id;
if (!id){
  router.push('/course/list');
}

request.get(`/system/course/${route.params.id}`).then(res => data.value = res.data.data);
</script>

<template>
    <div>
      <div class="breadcrumb-area-2 bg-overlay-3 relative bg-img" :style="{ backgroundImage: `url(${bg})`} ">
        <div class="container h-100">
          <div class="row h-100 align-items-center">
            <div class="col-12">
              <div class="breadcrumb-conetnt">
                <ul class="bread-list">
                  <li><a href="#">首页</a></li>
                  <li><i class="icon-down-arrow-11"></i></li>
                  <li>全栈WordPress开发者在线课程
                  </li>
                </ul>
                <h4 class="mb-0">全栈WordPress开发者在线课程
                </h4>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="course-details-area">
        <div class="container">
          <div class="row">
            <div class="col-lg-8">
              <div class="course-details-content-area">
                <div class="course-heading-details">
                  <div class="course-title">
                    <h2>{{ data.title }}</h2>
                    <a class="inner-btn" href="#">¥{{ data.price }}</a>
                  </div>
                  <div class="course-info-card-area">
                    <!-- Card -->
                    <div class="course-info-card">
                      <h4> 讲师:</h4>
                      <div class="course-auth-info-4 d-flex align-items-center">
                        <img class="auth-img" src="@/assets/img/bg-img/home-4/c-2.png" alt="">
                        <p>由 <a href="#">{{ data.teacher }}</a></p>
                      </div>
                    </div>
                    <!-- Card -->
                    <div class="course-info-card">
                      <h4>评价:</h4>
                      <p class="course-rating mb-0"><i class="icon-star1"></i> 4.5 <span>(0条评价)</span></p>
                    </div>
                    <!-- Card -->
                    <div class="course-info-card">
                      <h4>已报名:</h4>
                      <p class="course-desc"> {{ createRandomInt() }}名学生</p>
                    </div>
                    <!-- Card -->
                    <div class="course-info-card">
                      <h4>分类:</h4>
                      <p class="course-desc"> 编程语言</p>
                    </div>
                  </div>
                </div>
                <div class="course-bg-img">
                  <img src="@/assets/img/bg-img/inner/course.png" alt="">
                </div>
                <div class="course-detials-tab">
                  <ul class="nav nav-pills course-tab-title" id="pills-tab" role="tablist">
                    <li class="nav-item" role="presentation">
                      <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true"><i class="icon-inclined-pencil"></i> 课程描述</button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false" tabindex="-1"><i class="icon-icon_ribbon_alt"></i> 课程大纲</button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link" id="pills-contact-tab" data-bs-toggle="pill" data-bs-target="#pills-contact" type="button" role="tab" aria-controls="pills-contact" aria-selected="false" tabindex="-1"><i class="icon-price-list"></i> 讨论区</button>
                    </li>
                  </ul>
                  <div class="tab-content" id="pills-tabContent">
                    <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab" tabindex="0">
                      <div class="course-details-tab-content">
                        <h4>关于本课程</h4>
                        <p>{{ data.description }}</p>
                      </div>
                    </div>
                    <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab" tabindex="0">
                      <div class="course-details-tab-content">
                        <h4>课程大纲</h4>
                        <p>1. WordPress基础介绍</p>
                        <p>2. 主题开发</p>
                        <p>3. 插件开发</p>
                        <p>4. WordPress REST API</p>
                        <p>5. 性能优化</p>
                        <p>6. 安全性最佳实践</p>
                        <p>7. 部署与维护</p>
                      </div>
                    </div>
                    <div class="tab-pane fade" id="pills-contact" role="tabpanel" aria-labelledby="pills-contact-tab" tabindex="0">
                      <div class="course-details-tab-content">
                        <h4>课程讨论区</h4>
                        <p>在这里您可以与其他学员交流学习经验，提出问题并获得解答。我们的讲师团队会定期查看讨论区，确保您的问题得到及时回复。</p>
                        <p>请遵守社区规则，保持友善的讨论氛围。分享您的学习心得和项目经验，帮助他人共同进步。</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="other-ins-area">
                  <h4 class="other-heading">其他讲师:</h4>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="inst-member-info d-flex align-items-center">
                        <div class="inst-img">
                          <img src="@/assets/img/bg-img/t-1.jpg" alt="">
                        </div>
                        <div class="inst-info">
                          <h4>杰西卡·布朗</h4>
                          <p>副教授</p>
                          <ul class="inst-social-list">
                            <li><a href="#"><span class="icon-facebook-f"></span></a></li>
                            <li><a href="#"><span class="icon-instagram"></span></a></li>
                            <li><a href="#"><span class="icon-linkedin-in"></span></a></li>
                            <li><a href="#"><span class="icon-instagram"></span></a></li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="inst-member-info d-flex align-items-center">
                        <div class="inst-img">
                          <img src="@/assets/img/bg-img/t-2.jpg" alt="">
                        </div>
                        <div class="inst-info">
                          <h4>拉尔夫·爱德华兹</h4>
                          <p>助教</p>
                          <ul class="inst-social-list">
                            <li><a href="#"><span class="icon-facebook-f"></span></a></li>
                            <li><a href="#"><span class="icon-instagram"></span></a></li>
                            <li><a href="#"><span class="icon-linkedin-in"></span></a></li>
                            <li><a href="#"><span class="icon-instagram"></span></a></li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="inst-member-info d-flex align-items-center">
                        <div class="inst-img">
                          <img src="@/assets/img/bg-img/t-3.jpg" alt="">
                        </div>
                        <div class="inst-info">
                          <h4>拉尔夫·爱德华兹</h4>
                          <p>技术支持</p>
                          <ul class="inst-social-list">
                            <li><a href="#"><span class="icon-facebook-f"></span></a></li>
                            <li><a href="#"><span class="icon-instagram"></span></a></li>
                            <li><a href="#"><span class="icon-linkedin-in"></span></a></li>
                            <li><a href="#"><span class="icon-instagram"></span></a></li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="inst-member-info d-flex align-items-center">
                        <div class="inst-img">
                          <img src="@/assets/img/bg-img/t-1.jpg" alt="">
                        </div>
                        <div class="inst-info">
                          <h4>杰西卡·布朗</h4>
                          <p>副教授</p>
                          <ul class="inst-social-list">
                            <li><a href="#"><span class="icon-facebook-f"></span></a></li>
                            <li><a href="#"><span class="icon-instagram"></span></a></li>
                            <li><a href="#"><span class="icon-linkedin-in"></span></a></li>
                            <li><a href="#"><span class="icon-instagram"></span></a></li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- Faq area -->
                  <div class="faq-area course-details">
                    <div class="faq-content-area single-page-faq">
                      <h4>常见问题</h4>
                      <div class="accordion" id="accordionExample">
                        <div class="accordion-item  wow fadeInUp" data-wow-delay="700ms" style="visibility: visible; animation-delay: 700ms; animation-name: fadeInUp;">
                          <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                              1. 课程适合完全没有编程基础的人吗？
                            </button>
                          </h2>
                          <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                            <div class="accordion-body">
                              <p>本课程从基础开始讲解，适合零基础学员。我们会从HTML、CSS和PHP基础讲起，循序渐进地引导您掌握WordPress开发。</p>
                            </div>
                          </div>
                        </div>
                        <div class="accordion-item wow fadeInUp" data-wow-delay="900ms" style="visibility: visible; animation-delay: 900ms; animation-name: fadeInUp;">
                          <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                              2. 完成课程后能获得什么证书？
                            </button>
                          </h2>
                          <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                            <div class="accordion-body">
                              <p>完成所有课程并通过考核后，您将获得我们颁发的"全栈WordPress开发者"认证证书，这将有助于您在求职时展示您的技能。</p>
                            </div>
                          </div>
                        </div>
                        <div class="accordion-item  wow fadeInUp" data-wow-delay="1100ms" style="visibility: visible; animation-delay: 1100ms; animation-name: fadeInUp;">
                          <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                              3. 课程有有效期限制吗？
                            </button>
                          </h2>
                          <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                            <div class="accordion-body">
                              <p>课程购买后永久有效，您可以随时回看视频内容。我们还提供课程内容更新服务，确保您学习的是最新技术。</p>
                            </div>
                          </div>
                        </div>
                        <div class="accordion-item  wow fadeInUp" data-wow-delay="1300ms" style="visibility: visible; animation-delay: 1300ms; animation-name: fadeInUp;">
                          <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                              4. 遇到问题如何获得帮助？
                            </button>
                          </h2>
                          <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                            <div class="accordion-body">
                              <p>您可以通过课程讨论区提问，我们的讲师团队会在24小时内回复。对于复杂问题，我们还提供一对一辅导服务（需额外预约）。</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-4">
              <div class="sidebar-content-single-area">
                <div class="side-bar-video-area">
                  <div class="how-it-work-content-3 relative">
                    <div class="side-video-img relative">
                      <img src="@/assets/img/bg-img/home-3/how.png" alt="">
                    </div>
                    <div class="play-btn course">
                      <div class="video_player_btn">
                        <a href="https://www.youtube.com/watch?v=eEzD-Y97ges" class="popup-video"><span class="icon-Polygon-1"></span></a>
                      </div>
                    </div>
                  </div>
                  <div class="enroll-course-content">
                    <div class="enroll-heading text-center">
                      <a class="auth-btn w-100" href="login.html">立即报名</a>
                      <p>30天无理由退款保证</p>
                    </div>
                    <ul class="course-info-list">
                      <li>课时 : <span>0</span></li>
                      <li>时长 : <span>80小时30分钟</span></li>
                      <li>最大学生数 : <span>100</span></li>
                      <li>证书 : <span>有</span></li>
                    </ul>
                  </div>
                </div>
                <!-- Single Card -->
                <div class="cate-single-card">
                  <div class="cate-content-card">
                    <h3>分类</h3>
                    <ul class="cate-list">
                      <li><a href="#"><i class="icon-right ml-1"></i> 前端开发</a>
                        <span>4</span>
                      </li>
                      <li><a href="#"><i class="icon-right ml-1"></i> 后端开发</a>
                        <span>2</span>
                      </li>
                      <li><a href="#"><i class="icon-right ml-1"></i> 全栈开发</a>
                        <span>5</span>
                      </li>
                      <li><a href="#"><i class="icon-right ml-1"></i> WordPress </a>
                        <span>4</span>
                      </li>
                      <li><a href="#"><i class="icon-right ml-1"></i> PHP</a>
                        <span>2</span>
                      </li>
                      <li><a href="#"><i class="icon-right ml-1"></i> JavaScript </a>
                        <span>6</span>
                      </li>
                      <li><a href="#"><i class="icon-right ml-1"></i> 数据库</a>
                        <span>4</span>
                      </li>
                      <li><a href="#"><i class="icon-right ml-1"></i> 网页设计 </a>
                        <span>4</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<style scoped>

</style>