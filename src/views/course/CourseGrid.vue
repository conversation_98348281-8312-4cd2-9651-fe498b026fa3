<script setup lang="ts">

import CourseBreadcrumbe from "@/components/course/CourseBreadcrumbe.vue";
import {ref} from "vue";
import axios from "axios";
import {createRandomInt} from "@/utils/data.js";
// 导入背景图片
import shape2Img from "@/assets/img/bg-img/home-4/shape-2.png";
import Swal from "sweetalert2";
import request from "@/net";
import {useAccount} from "@/stores/user";

const account = useAccount();
const data = ref([]);

axios .get('http://localhost:8080/system/course/list').then(res => {
  data.value = res.data.rows;
})

function addCourseToCart(id) {
  request.post(`/system/item/add/${id}`)
      .then(({data}) => {
        account.cart.count++;
        Swal.fire({
          title: '添加成功',
          icon: 'success',
          showConfirmButton: false,
          timer: 1500
        })
      })
}
</script>

<template>
    <div>
      <course-breadcrumbe :course-count="data.length"/>
      <!-- 精选课程网格区域 -->
    <div class="featured-course-4-area single-page">
        <div class="container">
          <div class="row">
            <!-- 课程卡片 -->
            <div class="col-md-6 col-lg-4" v-for="course in data" :key="course.id">
              <div class="course-card-4-area">
                <!-- 装饰图形 -->
                <div class="care-shape-4 wow fadeInDown" data-wow-delay="700ms" style="visibility: visible; animation-delay: 700ms; animation-name: fadeInDown;">
                  <img src="@/assets/img/bg-img/home-2/care-shap.png" alt="装饰图形">
                </div>
                <div class="course-img-4 bg-img" :style="{ backgroundImage: `url(${shape2Img})` }">
                  <div class="course-sub-title">
                    <h6>PHP 初学者</h6>
                  </div>
                  <div class="course-title-img">
                    <img src="@/assets/img/bg-img/home-4/c-1.png" alt="课程封面">
                  </div>
                  <!-- 装饰图形 -->
                  <div class="course-shape-4">
                    <img src="@/assets/img/bg-img/home-4/shape-3.png" alt="装饰图形">
                  </div>

                  <div class="course-offer-4">
                    <div class="offer-bg-shape relative">
                      <img src="@/assets/img/bg-img/home-4/shape-4.png" alt="优惠标签背景">
                    </div>
                    <div class="offer-content-4 ">
                      <p>-20%</p>
                      <span>优惠</span>
                    </div>

                  </div>
                </div>
                <!-- 课程内容 -->
                <div class="course-content-info-4">
                  <div class="course-content-rating d-flex justify-content-between align-items-center">
                    <p><i class="icon-star1"></i> 4.5 <span>(0 条评价)</span></p>
                    <div class="ribbon-icon">
                      <i class="icon-icon_ribbon_alt"></i>
                    </div>
                  </div>
                  <!-- 课程信息 -->
                  <div class="course-info-meta-4 d-flex align-items-center">
                    <p><i class="icon-Home"></i> {{ createRandomInt() }} 名学员</p>
                    <p><i class="icon-book-solid-1"></i> {{ course.lessone }} 节课</p>
                  </div>
                  <h2><a href="course-details.html">{{ course.title }}</a></h2>
                  <p class="course-desc-4">{{ course.description }}</p>
                  <div class="auth-info-4 d-flex align-items-center">
                    <img class="auth-img" src="@/assets/img/bg-img/home-4/c-2.png" alt="讲师头像">
                    <p>讲师：<a href="#">{{ course.teacher }}</a></p>
                  </div>
                  <!-- 价格和购物车 -->
                  <div class="course-footer-4 d-flex align-items-center justify-content-between">
                    <div class="course-price">
                      <p>¥{{ (course.price * 0.8).toFixed(0) }} <span>¥{{ course.price }}</span></p>
                    </div>
                    <div class="course-buy" style="cursor: pointer;">
                      <p><a @click="addCourseToCart(course.id)"><i class="icon-icon_cart_alt"></i> <span>加入购物车</span></a>
                      </p>
                    </div>
                  </div>

                </div>


              </div>
            </div>
            <!-- 加载更多按钮 -->
            <div class="col-12">
              <div class="load-btn-area text-center mt-20">
                <a class="load-btn" href="#"><i class="icon-icon_document_alt"></i> 加载更多</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<style scoped>

</style>