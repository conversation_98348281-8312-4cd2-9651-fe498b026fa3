<script setup>
import {ref} from "vue";

import {createRandomInt} from "@/utils/data.js";
import CourseBreadcrumbe from "@/components/course/CourseBreadcrumbe.vue";
import request from "@/net";
import axios from "axios";

const data = ref([]);

request.get('/system/course/list').then(res => {
    data.value = res.data.rows;
})
</script>

<template>
    <div>
      <course-breadcrumbe/>
      <!-- 精选课程列表区域 -->
      <div class="featured-course-4-area single-page">
        <div class="container">
          <div class="row">
            <!-- 课程卡片 -->
            <div class="col-12" v-for="course in data" :key="course.id">
              <div class="course-card-4-area course-list d-flex ">
                <div class="course-img-4 single">
                  <div class="cr-img relative">
                    <img src="@/assets/img/bg-img/inner/list-1.png" alt="JavaScript初学者课程">
                  </div>
                </div>
                <!-- 课程内容 -->
                <div class="course-content-info-4 single">
                  <div class="course-content-rating d-flex justify-content-between align-items-center">
                    <p><i class="icon-star1"></i> 4.5 <span>(0 条评价)</span></p>
                    <div class="ribbon-icon">
                      <i class="icon-icon_ribbon_alt"></i>
                    </div>
                  </div>
                  <h2><a :href="`/course/detail/${course.id}`">{{ course.title }}</a></h2>
                  <!-- 课程信息 -->
                  <div class="course-info-meta-4 d-flex align-items-center">
                    <p><i class="icon-Home"></i> {{ createRandomInt() }} 名学员正在学习</p>
                    <p><i class="icon-book-solid-1"></i>共 {{ course.lessone }} 节课</p>
                  </div>

                  <p class="course-desc-4">{{ course.description }}</p>
                  <div class="auth-info-4 d-flex align-items-center">
                    <img class="auth-img" src="@/assets/img/bg-img/home-4/c-2.png" alt="讲师头像">
                    <p>讲师：<a href="#">{{ course.teacher }}</a></p>
                  </div>
                  <!-- 价格和购物车 -->
                  <div class="course-footer-4 d-flex align-items-center justify-content-between">
                    <div class="course-price">
                      <p>¥{{ (course.price * 0.8).toFixed(0) }} <span>¥{{ course.price }}</span></p>
                    </div>
                    <div class="course-buy">
                      <p><a href="#"><i class="icon-icon_cart_alt"></i> <span>加入购物车</span></a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 加载更多按钮 -->
            <div class="col-12">
              <div class="load-btn-area text-center mt-20">
                <a class="load-btn" href="all-course.html"><i class="icon-icon_document_alt"></i> 加载更多</a>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
</template>

<style scoped>

</style>